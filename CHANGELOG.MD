# Version 1.6.29

bTeam:

* Implemented Contacts

# Version 1.6.28

bTeam:

* Revert Android SDK to 34 due to causing issues with scrolling

# Version 1.6.27

bTeam:

* Fix safeArea for android 15.
* Refresh folders counters when opening a burger menu.

------------------------------------------------

# Version 1.6.11

bTeam:

* Show notified users and participants on all comments (Message, Notifications, Cases)
* Create pipelines for deploying to TestFlight and deployng to Firebase

------------------------------------------------

# Version 1.6.10

bTeam:

* Fix for link case tab in order to show from field instead of assignTo.

------------------------------------------------

# Version 1.6.9

bTeam:

* Notification quick search && keyword. criterion in adv search.
* Add email icons in message list.
* Add case comments.
* Add linked emails tab.
* Add linked cases tab.
* Add case's task tab.
* Make inbox list animated. (Hide/show search bar when scrolling).

------------------------------------------------
