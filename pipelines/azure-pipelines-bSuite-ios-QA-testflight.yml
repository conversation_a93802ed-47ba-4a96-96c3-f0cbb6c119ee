resources:
  pipelines:
  - pipeline: qa_build
    source: bSuite iOS QA Build
    trigger: true

jobs:
- job: AppCenter_release
  pool:
      vmImage: 'macos-latest'

  steps:
  - checkout: none
  - download: qa_build
    patterns: |
      iOS_bin/*.ipa*

  # - task: AppStoreRelease@1
  #   inputs:
  #     serviceEndpoint: 'bSuite iOS Testflight Release'
  #     releaseTrack: 'TestFlight'
  #     appIdentifier: 'com.benefit.bsuite'
  #     appType: 'iOS'
  #     ipaPath: '$(Pipeline.Workspace)/qa_build/iOS_bin/bSeafarer.ipa'
  #     appSpecificId: '1445159385'

  - task: AppStoreRelease@1
    inputs:
      serviceEndpoint: 'bSuite iOS Testflight Release'
      releaseTrack: 'TestFlight'
      appIdentifier: 'com.benefit.bsuite'
      appType: 'iOS'
      ipaPath: '$(Pipeline.Workspace)/qa_build/iOS_bin/bSeafarer.ipa'
      appSpecificId: '1445159385'
      installFastlane: false