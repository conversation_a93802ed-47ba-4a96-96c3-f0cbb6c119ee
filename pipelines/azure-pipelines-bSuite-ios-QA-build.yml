trigger: none

resources:
  repositories:
    - repository: bMobiles
      type: git
      name: Mobile apps/bMobiles
      # trigger:
      #   branches:
      #     include:
      #     - qa

variables:
  certFile: CertificatesBSuiteProd.p12
  provisioningProfileFile: 'AppCenterAdHoc.mobileprovision'

jobs:
- job: iOS_build
  timeoutInMinutes: 120
  pool:
    vmImage: 'macOS-14'

  variables:
    Project: 'Mobile apps'
    Repo: bMobiles
    Branch: development
    platformPrefix: 'packages/engine'
    Configuration: Release
    Workspace: bSuite
    Scheme: bSuite
    Sdk: iphoneos
    OutDir: '$(Repo)/$(platformPrefix)/ios/build/Build/Products/'
    provisioningProfileName: 'AppCenterAdHoc'
    signingIdentity: 'Apple Distribution: BENEFIT SOFTWARE (FTRP3S9CH8)'
    legacyPeerDeps: true
    Xcode: 15.3

  steps:
  - checkout: self
  - checkout: git://${{ variables['Project'] }}/${{ variables['Repo'] }}@${{ variables['Branch'] }}

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        brew install jq
        cd packages/engine
        npx --yes react-native-rename@latest "bSuite" -b "com.benefit.bsuite"
        echo $(cat package.json | jq '.name = "engine"' ) > package.json
        cp -f ios/bSuite/GoogleService-Info-bSuite.plist ios/bSuite/GoogleService-Info.plist
        cp -f index.js.bsuite index.js
  #      git submodule update
  #      cd packages/engine/react-native-ci-tools
  #      npm install
  #      node index.js bundle "com.benefit.bsuite" "bSuite" -i --directory ../
      workingDirectory: '$(Repo)'
  
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        ENV_WHITELIST=${ENV_WHITELIST:-"^RN_"}
        set | egrep -e $ENV_WHITELIST | sed 's/^RN_//g' | tee .env
        cat .env
      workingDirectory: '$(Repo)'
    env:
      RN_API_URL: http://**************/api
      RN_API_URL_BSUITE: http://*************/api
      RN_EDIT: false
      RN_NOTIFICATIONS: false
      RN_COMMENTS: true
      RN_APPROVALS: true
      RN_SUBMISSION_LIST: false
      RN_TENANT: bdevwebapps.onmicrosoft.com
      RN_CLIENT_ID: 0c65eca3-eb49-48a3-954b-bc0dc3f20e59
      RN_USER_FLOW_POLICY: B2C_1_3dParty_SignInSignUp
      RN_TOKEN_URI: https://bdevwebapps.b2clogin.com/bdevwebapps.onmicrosoft.com/oauth2/v2.0/token
      RN_AUTHORITY_HOST: https://bdevwebapps.b2clogin.com/bdevwebapps.onmicrosoft.com/oauth2/v2.0/authorize
      RN_REDIRECT_URI: https://aadredirection.azurewebsites.net/
      RN_SCOPE_API_ACCESS: https://bdevwebapps.onmicrosoft.com/0a594958-5083-4cc6-93bf-a7b2362e60e9/API.Access
      RN_APP_BUNDLE: bSuite
      RN_COMPANY_USER_REGISTRY: https://company-registry-user-facing-dev.azurewebsites.net/api
      RN_COMPANY_REGISTRY_URL: https://company-registry-dev.azurewebsites.net/api
      RN_COMPANY_REGISTRY_CODE: LuQBZb7aF-nSt0R6KXu_kr-zID81IudSbZsxq9WUkXC-AzFuvgqbWA==

  - task: Bash@3
    displayName: Copy environment
    inputs:
      targetType: 'inline'
      script: 'cp $(Repo)/.env $(Repo)/$(platformPrefix)'

  - task: Bash@3
    displayName: Set Xcode version
    inputs:
      targetType: 'inline'
      script: |
        xcodeRoot=/Applications/Xcode_$(Xcode).app
        echo '##vso[task.setvariable variable=MD_APPLE_SDK_ROOT;]'$xcodeRoot;sudo xcode-select --switch $xcodeRoot/Contents/Developer

  - task: InstallAppleCertificate@2
    displayName: Install certificate
    inputs:
      certSecureFile: $(certFile)
      certPwd: '$(iOSCertPass)'
      keychain: 'temp'

  - task: InstallAppleProvisioningProfile@1
    displayName: Install provisioning profile
    inputs:
      provisioningProfileLocation: 'secureFiles'
      provProfileSecureFile: $(provisioningProfileFile)
      
  # Node.js tool installer v0
  # Finds or downloads and caches the specified version spec of Node.js and adds it to the PATH.
  - task: NodeTool@0
    inputs:
      versionSource: 'spec' # 'spec' | 'fromFile'. Required. Source of version. Default: spec.
      versionSpec: '18.20.0' # string. Optional. Use when versionSource = spec. Version Spec. Default: 6.x.

  - task: Npm@1
    displayName: Npm install
    inputs:
      command: 'install'
      workingDir: '$(Repo)'
    
  - task: Npm@1
    displayName: Npm install workspace
    inputs:
      command: 'custom'
      workingDir: '$(Repo)'
      customCommand: 'install --workspace=engine --include-workspace-root=true'
 
  - task: CocoaPods@0
    displayName: Pod install
    inputs:
      workingDirectory: '$(Repo)/$(platformPrefix)/ios'
      forceRepoUpdate: false

  - task: Xcode@5
    displayName: Build project
    inputs:
      actions: 'build'
      xcWorkspacePath: '$(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace'
      scheme: '$(Scheme)'
      packageApp: true
      archivePath: '$(OutDir)/Archive'
      exportPath: '$(OutDir)'
      signingOption: 'manual'
      signingIdentity: '$(signingIdentity)'
      provisioningProfileName: '$(provisioningProfileName)'
    condition: or(eq(variables.autoSigning, 'false'), eq(variables.autoSigning, ''))
      
  - task: Xcode@5
    displayName: Build project
    inputs:
      actions: 'build'
      xcWorkspacePath: '$(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace'
      scheme: '$(Scheme)'
      packageApp: true
      archivePath: '$(OutDir)/Archive'
      exportPath: '$(OutDir)'
      signingOption: 'default'
    condition: eq(variables.autoSigning, 'true')
 
  - task: PublishBuildArtifacts@1
    displayName: Publish artifact
    inputs:
      PathtoPublish: '$(OutDir)'
      ArtifactName: 'iOS_bin'
      publishLocation: 'Container'
