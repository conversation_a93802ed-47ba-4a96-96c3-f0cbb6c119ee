trigger: none

resources:
  repositories:
    - repository: bMobiles
      type: git
      name: Mobile apps/bMobiles
      # trigger:
      #   branches:
      #     include:
      #     - qa

variables:
  certFile: CertificatesBSuiteProd.p12
  provisioningProfileFile: 'new_bTeam_provision.mobileprovision'
  # Project-specific build destination settings
  iOSDestinations: '-destination "generic/platform=iOS,name=iPhone" -verbose TARGETED_DEVICE_FAMILY="1" SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD="NO"'

jobs:
- job: iOS_build
  timeoutInMinutes: 120
  pool:
    vmImage: 'macOS-14'

  variables:
    Project: 'Mobile apps'
    Repo: bMobiles
    Branch: master
    platformPrefix: 'packages/engine'
    Configuration: Release
    Workspace: bTeam
    Scheme: bTeam
    Sdk: iphoneos
    OutDir: '$(Repo)/$(platformPrefix)/ios/build/Build/Products/'
    provisioningProfileName: 'new bTeam provision'
    signingIdentity: 'Apple Distribution: BENEFIT SOFTWARE (FTRP3S9CH8)'
    legacyPeerDeps: true
    Xcode: 16.2

  steps:
  - checkout: self
  - checkout: git://${{ variables['Project'] }}/${{ variables['Repo'] }}@${{ variables['Branch'] }}

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        brew install jq
        cd packages/engine
        npx --yes react-native-rename@latest "bTeam" -b "gr.benefit.bteam"
        echo $(cat package.json | jq '.name = "engine"' ) > package.json
        rm -rf ios/bTeam/Images.xcassets
        cp -Rf ios/appIconReplacements-bTeam/Images.xcassets ios/bTeam
        cp -f ios/bTeam/GoogleService-Info-bTeam.plist ios/bTeam/GoogleService-Info.plist
        cp -f index.js.bsuite index.js
      workingDirectory: '$(Repo)'

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        ENV_WHITELIST=${ENV_WHITELIST:-"^RN_"}
        set | egrep -e $ENV_WHITELIST | sed 's/^RN_//g' | tee .env
        cat .env
      workingDirectory: '$(Repo)'
    env:
      RN_APP_BUNDLE: bTeam
      RN_API_URL_BTEAM: https://bteam.benmaritime.eu/hermeswebapi
      RN_API_URL_BTEAM_MESSAGES: https://bteam.benmaritime.eu/hermesmessages
  - task: Bash@3
    displayName: Copy environment
    inputs:
      targetType: 'inline'
      script: 'cp $(Repo)/.env $(Repo)/$(platformPrefix)'

  - task: Bash@3
    displayName: Set Xcode version
    inputs:
      targetType: 'inline'
      script: |
        xcodeRoot=/Applications/Xcode_$(Xcode).app
        echo '##vso[task.setvariable variable=MD_APPLE_SDK_ROOT;]'$xcodeRoot;sudo xcode-select --switch $xcodeRoot/Contents/Developer

  - task: InstallAppleCertificate@2
    displayName: Install certificate
    inputs:
      certSecureFile: $(certFile)
      certPwd: '$(iOSCertPass)'
      keychain: 'temp'

  - task: InstallAppleProvisioningProfile@1
    displayName: Install provisioning profile
    inputs:
      provisioningProfileLocation: 'secureFiles'
      provProfileSecureFile: $(provisioningProfileFile)

  # Node.js tool installer v0
  # Finds or downloads and caches the specified version spec of Node.js and adds it to the PATH.
  - task: NodeTool@0
    inputs:
      versionSource: 'spec' # 'spec' | 'fromFile'. Required. Source of version. Default: spec.
      versionSpec: '18.20.0' # string. Optional. Use when versionSource = spec. Version Spec. Default: 6.x.

  - task: Npm@1
    displayName: Npm install
    inputs:
      command: 'install'
      workingDir: '$(Repo)'

  - task: Npm@1
    displayName: Npm install workspace
    inputs:
      command: 'custom'
      workingDir: '$(Repo)'
      customCommand: 'install --workspace=engine --include-workspace-root=true'

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        echo "uninstalling all cocoapods versions"
        sudo gem uninstall cocoapods -ax
        echo "installing cocoapods version latest"
        sudo gem install cocoapods --pre


  - task: CocoaPods@0
    displayName: Pod install
    inputs:
      workingDirectory: '$(Repo)/$(platformPrefix)/ios'
      forceRepoUpdate: false

  - task: Xcode@5
    displayName: Build project
    inputs:
      actions: 'build'
      xcWorkspacePath: '$(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace'
      scheme: '$(Scheme)'
      packageApp: true
      archivePath: '$(OutDir)/Archive'
      exportPath: '$(OutDir)'
      signingOption: 'manual'
      signingIdentity: '$(signingIdentity)'
      provisioningProfileName: '$(provisioningProfileName)'
      args: '$(iOSDestinations)'
      useXCPretty: false
    condition: or(eq(variables.autoSigning, 'false'), eq(variables.autoSigning, ''))

  - task: Xcode@5
    displayName: Build project
    inputs:
      actions: 'build'
      xcWorkspacePath: '$(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace'
      scheme: '$(Scheme)'
      packageApp: true
      archivePath: '$(OutDir)/Archive'
      exportPath: '$(OutDir)'
      signingOption: 'default'
      args: '$(iOSDestinations)'
    condition: eq(variables.autoSigning, 'true')

  - task: PublishBuildArtifacts@1
    displayName: Publish artifact
    inputs:
      PathtoPublish: '$(OutDir)'
      ArtifactName: 'iOS_bin'
      publishLocation: 'Container'
