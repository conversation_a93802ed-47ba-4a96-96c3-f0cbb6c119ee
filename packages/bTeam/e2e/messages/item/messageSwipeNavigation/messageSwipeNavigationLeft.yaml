# Press the first message.
# Navigate to the next message using swipe navigation.
# Expect to see different subject.

# messageSwipeNavigationLeft.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "messageList--item-0"
    index: 0
- copyTextFrom:
    id: "message--subject"
- evalScript: ${output.messageSubjectFirst = maestro.copiedText}
- swipe:
    direction: "LEFT"
    timeout: 5000
- copyTextFrom:
    id: "message--subject"
- evalScript: ${output.messageSubjectSecond = maestro.copiedText}
- assertTrue: ${output.messageSubjectFirst != output.messageSubjectSecond}
