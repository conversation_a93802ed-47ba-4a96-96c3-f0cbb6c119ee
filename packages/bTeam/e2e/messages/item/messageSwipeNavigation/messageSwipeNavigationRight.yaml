# Press the second message.
# Navigate to the previous message using swipe navigation.
# Expect to see different subject.

# messageSwipeNavigationRight.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "messageList--item-1"
    index: 0
- copyTextFrom:
    id: "message--subject"
- evalScript: ${output.messageSubjectSecond = maestro.copiedText}
- swipe:
    direction: "RIGHT"
    timeout: 5000
- copyTextFrom:
    id: "message--subject"
- evalScript: ${output.messageSubjectFirst = maestro.copiedText}
- assertTrue: ${output.messageSubjectFirst != output.messageSubjectSecond}
