# Copy first two message bodies
# Long press a message
# Select another message
# Press delete button from bottomSheet
# Expect to not see the two copied bodies

# deleteMultipleMessages.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- copyTextFrom:
    id: "0-messageList--bodyText"
- evalScript: ${output.firstEmailBody = maestro.copiedText}
- copyTextFrom:
    id: "1-messageList--bodyText"
- evalScript: ${output.secondEmailBody = maestro.copiedText}
- assertVisible:
    ${output.firstEmailBody}
- assertVisible:
    ${output.secondEmailBody}
- longPressOn:
    id: "messageList--item-0"
    index: 0
- longPressOn:
    id: "messageList--item-1"
    index: 0
- tapOn:
    id: "inbox__bottomSheet--deleteButton"
- assertNotVisible:
    ${output.firstEmailBody}
- assertNotVisible:
    ${output.secondEmailBody}
