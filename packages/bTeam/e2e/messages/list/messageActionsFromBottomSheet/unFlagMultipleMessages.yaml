# Filter messages to see only flagged
# Long press a message
# Select another message
# Press more from bottomSheet menu
# Press unFlag
# Expect multiSelect to be closed
# Expect first two messages to be flagged

# unFlagMultipleMessages.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--flagged"
- tapOn:
    id: "bottomSheetOption-Yes"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertVisible:
    id: "0-messageList--flagIcon"
- assertVisible:
    id: "1-messageList--flagIcon"
- longPressOn:
    id: "messageList--item-0"
    index: 0
- longPressOn:
    id: "messageList--item-1"
    index: 0
- tapOn:
    id: "inbox__bottomSheet--moreButton"
- tapOn:
    id: "inbox__bottomSheet--flagButton"
- assertNotVisible:
    id: "0-messageList--unCheckedCheckbox"
- assertNotVisible:
    id: "1-messageList--checkedCheckbox"
- assertVisible:
    id: "0-messageList--unFlagIcon"
- assertVisible:
    id: "1-messageList--unFlagIcon"
