# Filter messages to show only unread messages
# Long press a message
# Select another message
# Press MarkAsRead button from bottomSheet
# Expect to not see bold username for first two messages

# markAsReadMultipleMessages.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "advancedSearchCriteriaButton"
- swipe:
    start: 56%,61%
    end: 65%,31%
    duration: 699
- tapOn:
    id: "inboxSearchFilters__fields--readUnread"
- tapOn:
    id: "bottomSheetOption-Unread"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertVisible:
    id: "0-messageList--usernameBoldText"
    index: 0
- assertVisible:
    id: "1-messageList--usernameBoldText"
    index: 0
- longPressOn:
    id: "messageList--item-0"
    index: 0
- longPressOn:
    id: "messageList--item-1"
    index: 0
- tapOn:
    id: "inbox__bottomSheet--markAs<PERSON><PERSON>Button"
- assertNotVisible:
    id: "0-messageList--usernameBoldText"
    index: 0
- assertNotVisible:
    id: "1-messageList--usernameBoldText"
    index: 0
