# Long press a message
# Select another message
# Press more from bottomSheet menu
# Press Copy to folder
# Select user1 folder and press move. (We need to have user1 folder)
# Expect to see success message

# copyToFolderMultipleMessages.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- longPressOn:
    id: "messageList--item-0"
    index: 0
- longPressOn:
    id: "messageList--item-1"
    index: 0
- tapOn:
    id: "inbox__bottomSheet--moreButton"
- tapOn:
    id: "inbox__bottomSheet--moveToFolderButton"
- tapOn: "user1"
- tapOn:
    id: "foldersList--moveButton"
- assertVisible: "Email copied successfully to folder"
