# Copy body of the first message
# Select first message
# Press delete icon
# Delete modal is visible
# Press Delete button
# Expect to return to inbox screen
# Expect to not see copied body of first message

# deleteMessage.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- copyTextFrom:
    id: "0-messageList--bodyText"
- evalScript: ${output.firstEmailBody = maestro.copiedText}
- tapOn:
    id: "messageList--item-0"
    index: 0
- tapOn:
    id: "message__navigationHeader--trashIcon"
- tapOn:
    id: "message__deleteModal--deleteOrRestoreButton"
- assertVisible:
    id: "advancedSearchCriteriaButton"
- assertNotVisible:
    ${output.firstEmailBody}
