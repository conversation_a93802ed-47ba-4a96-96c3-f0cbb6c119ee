# Filter messages to show only unFlagged
# Select a message
# Press flag icon
# Expect to see unflag instead of flag icon

# flagMessage.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "advancedSearchCriteriaButton"
- swipe:
    start: "50%,69%"
    end: "45%,30%"
    duration: 890
- tapOn:
    id: "inboxSearchFilters__fields--flagged"
- tapOn:
    id: "bottomSheetOption-No"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- tapOn:
    id: "messageList--item-0"
    index: 0
- assertVisible:
    id: "message__navigationHeader--notFlaggedIcon"
- tapOn:
    id: "message__navigationHeader--notFlaggedIcon"
- assertVisible:
    id: "message__navigationHeader--flaggedIcon"
