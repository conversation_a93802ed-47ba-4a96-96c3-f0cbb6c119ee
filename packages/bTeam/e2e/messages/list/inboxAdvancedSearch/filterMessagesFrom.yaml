# In inbox screen press adc criteria button
# Filter messages with specific sender
# Navigate to first message
# Expect sender to be correct

# filterMessagesFrom.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--from"
- inputText: "<EMAIL>"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- tapOn:
    id: "messageList--item-0"
    index: 0
- assertVisible: ".*<EMAIL>.*"
