# In inbox screen press adc criteria button
# Filter messages with attachments only
# Expect first two messages to have attachment icon

# filterMessagesWithAttachments.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--hasAttachments"
- tapOn:
    id: "bottomSheetOption-Yes"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertVisible:
    id: "0-messageList__bottomIcons--hasAttachments"
- assertVisible:
    id: "1-messageList__bottomIcons--hasAttachments"
