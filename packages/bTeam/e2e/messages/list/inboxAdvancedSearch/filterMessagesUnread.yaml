# In inbox screen press adc criteria button
# Filter unread messages only
# Expect first two messages to be unread

# filterMessagesUnread.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--readUnread"
- tapOn:
    id: "bottomSheetOption-Unread"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertVisible:
    id: "0-messageList--usernameBoldText"
- assertVisible:
    id: "1-messageList--usernameBoldText"
