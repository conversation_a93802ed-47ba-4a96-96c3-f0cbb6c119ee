# In inbox screen press adc criteria button
# Filter messages with specific keyword
# Expect this word to be in inbox screen

# filterMessagesBasedOnKeyword.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--keywords"
- inputText: "test"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertVisible: ".*test.*"
