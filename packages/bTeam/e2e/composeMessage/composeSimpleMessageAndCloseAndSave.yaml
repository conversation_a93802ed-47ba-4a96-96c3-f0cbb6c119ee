# Compose message and press close button and save message as draft

# composeSimpleMessageAndCloseAndSave.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "inbox--composeIcon"
- tapOn:
    id: "compose__fields--to"
- inputText: "<EMAIL>"
- tapOn:
    id: "compose__fields--subject"
- inputText: "some subject"
- tapOn:
    id: "compose__fields--body"
- inputText: "Lorem Ipsum is simply dummy text of the printing and typesetting industry."
- tapOn:
    id: "compose__header--closeButton"
- assertVisible: "Unsaved Changes"
- tapOn: "CANCEL"
- tapOn:
    id: "compose__header--closeButton"
- tapOn: "SAVE"
- assertVisible:
    id: "inbox--composeIcon"
- tapOn: "Drafts"
- assertVisible: "test draft subject"
- tapOn:
    id: "inbox__header--burgerIcon"
