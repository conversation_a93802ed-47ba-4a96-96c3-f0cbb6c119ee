# Compose a full message with all fields populated and attachment and send

# composeSimpleMessageAndCheckFieldsValidation.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "inbox--composeIcon"
- tapOn:
    id: "compose__fields--to"
- inputText: "wrong email format"
- assertVisible:
    id: "compose__fields--fromErrorMessage"
- tapOn:
    id: "compose--expandFieldsButton"
- tapOn:
    id: "compose__fields--cc"
- inputText: "wrong email format"
- assertVisible:
    id: "compose__fields--ccErrorMessage"
- inputText: "wrong email format"
- assertVisible: "Please enter a valid Bcc email address."
- tapOn:
    id: "compose__header--sendMessageButton"
- tapOn: "OK"
- tapOn:
    id: "compose__fields--bcc"
