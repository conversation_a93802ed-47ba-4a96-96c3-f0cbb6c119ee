# Compose simple message and send

# composeSimpleMessageAndSend.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "inbox--composeIcon"
- tapOn:
    id: "compose__fields--to"
- inputText: "<EMAIL>"
- tapOn:
    id: "compose__fields--subject"
- inputText: "some subject"
- tapOn:
    id: "compose__fields--body"
- inputText: "Lorem Ipsum is simply dummy text of the printing and typesetting industry."
- tapOn:
    id: "compose__header--sendMessageButton"
- assertVisible: "Message sent successfully."
- tapOn: "OK"
- assertVisible:
    id: "inbox--composeIcon"
