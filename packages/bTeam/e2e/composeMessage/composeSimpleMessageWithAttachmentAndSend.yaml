# Compose simple message with attachment and send
# This test requires a test.pdf file to be present in the files directory

# composeSimpleMessageWithAttachmentAndSend.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "inbox--composeIcon"
- tapOn:
    id: "compose__fields--to"
- inputText: "<EMAIL>"
- tapOn:
    id: "compose__fields--subject"
- inputText: "some subject"
- tapOn:
    id: "compose__fields--body"
- inputText: "Lorem Ipsum is simply dummy text of the printing and typesetting industry."
- tapOn:
    id: "compose__header--addAttachmentButton"
- tapOn:
    text: "test.pdf"
    index: 0
- assertVisible:
    id: "compose__header--attachmentCount"
    text: 1
- tapOn:
    id: "compose__header--sendMessageButton"
- assertVisible: "Message sent successfully."
- tapOn: "OK"
- assertVisible:
    id: "inbox--composeIcon"
