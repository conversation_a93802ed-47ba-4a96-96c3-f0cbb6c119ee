# Scenario: User logout
#   Navigate to the Menu tab
#   Press "Logout"
#   Verify that the user is logged out successfully

# logout.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "tabList--menu"
- assertVisible:
    id: "menu--logoutButton"
- tapOn:
    id: "menu--logoutButton"
- assertVisible:
    id: "login--inputUrl"
- assertVisible:
    id: "login--inputUsername"
- assertVisible:
    id: "login--inputPassword"




