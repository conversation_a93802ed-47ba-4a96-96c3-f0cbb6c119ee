# Scenario: PullToRefresh contact list
#   Pull to refresh
#   Expect to see list items

# pullToRefreshContactList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---
- runFlow:
    file: ../login/login.yaml

- tapOn:
    id: "tabList--cases"
- tapOn:
    ", CAS-ds, sadasdasda1111111111111, Assigned to: admin, Added on,  May28,\
    \ 11:04AM"
- assertVisible:
    id: "cases__caseInfoHeader--refTitle"
- assertVisible:
    id: "cases__caseInfoHeader--subjectText"
- assertVisible:
    id: "cases__caseInfoHeader--refTitle"
- tapOn:
    id: "cases__caseScreen--tabTaskList"
- assertVisible: "Tasks"
- assertVisible:
    "Ref: 01, sadasdasda1111111111111, Assigned to: admin, Type: Test1,\
    \ Description:, Due to,  May 28, 2025, Activated, sadasdasda1111111111111"
