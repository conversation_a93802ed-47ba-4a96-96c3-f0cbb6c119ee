# Scenario: Initial render of case list
#   Tap in cases tab
#   Expect to have at least two elements in the list

# initialRenderCaseList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
env:
  SEARCH_TEXT: test
---

- runFlow:
    file: ../../login/login.yaml
- tapOn:
    id: "tabList--cases"
- assertVisible:
    id: "caseList--listItem-0"
- assertVisible:
    id: "caseList--listItem-1"



