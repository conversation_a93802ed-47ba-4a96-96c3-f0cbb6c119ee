# Scenario: PullToRefresh contact list
#   Pull to refresh
#   Expect to see list items

# pullToRefreshContactList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---
- runFlow:
    file: ../login/login.yaml

- tapOn:
    id: "tabList--cases"
- tapOn:
    ", CAS-ds, sadasdasda1111111111111, Assigned to: admin, Added on,  May28,\
    \ 11:04AM"
- assertVisible:
    id: "cases__caseInfoHeader--refTitle"
- assertVisible:
    id: "cases__caseInfoHeader--subjectText"
- assertVisible:
    id: "cases__caseInfoHeader--refTitle"
- tapOn:
    id: "cases__caseScreen--tabComments"
- assertVisible: "Comments"
