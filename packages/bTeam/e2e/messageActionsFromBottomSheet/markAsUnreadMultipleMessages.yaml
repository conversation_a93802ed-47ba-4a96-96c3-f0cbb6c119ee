# Filter messages to show only read messages
# Long press a message
# Select another message
# Press MarkAsUnread button from bottomSheet
# Expect to see bold username for first two messages

# markAsUnreadMultipleMessages.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "advancedSearchCriteriaButton"
- swipe:
    start: 56%,61%
    end: 65%,31%
    duration: 699
- tapOn:
    id: "inboxSearchFilters__fields--readUnread"
- tapOn:
    id: "bottomSheetOption-Unread"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertNotVisible:
    id: "0-messageList--usernameBoldText"
    index: 0
- assertNotVisible:
    id: "1-messageList--usernameBoldText"
    index: 0
- longPressOn:
    id: "messageList--item-0"
    index: 0
- longPressOn:
    id: "messageList--item-1"
    index: 0
- tapOn:
    id: "inbox__bottomSheet--markAsReadButton"
- assertVisible:
    id: "0-messageList--usernameBoldText"
    index: 0
- assertVisible:
    id: "1-messageList--usernameBoldText"
    index: 0
