# Long press a message
# Press checkbox in navigation header to select all messages
# Expect all messages to be selected
# Press again checkbox in navigation header to unSelect all messages
# Expect all messages to be unSelected

# selectUnSelectAllMessages.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- longPressOn:
    id: "messageList--item-0"
    index: 0
- tapOn:
    id: "inbox__navigationHeader--selectAllCheckbox"
- assertNotVisible:
    id: "0-messageList--notCheckedCheckbox"
- assertNotVisible:
    id: "1-messageList--notCheckedCheckbox"
- assertVisible:
    id: "0-messageList--checkedCheckbox"
- assertVisible:
    id: "1-messageList--checkedCheckbox"

- tapOn:
    id: "inbox__navigationHeader--selectAllCheckbox"
- assertVisible:
    id: "0-messageList--unCheckedCheckbox"
- assertVisible:
    id: "1-messageList--unCheckedCheckbox"
- assertNotVisible:
    id: "0-messageList--checkedCheckbox"
- assertNotVisible:
    id: "1-messageList--checkedCheckbox"
