# Scenario: Search notificationList with keyword in searchBar
#   In NotificationList screen, type a keyword in the search input
#   Verify that matching notifications appear in the results
#   AssertVisible that the keyword appears at least twice. One in searchInput and one more in the list

# filterNotificationsBasedOnSearchBar.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "tabList--notifications"
- tapOn:
    id: "notificationList--searchInput"
- inputText: "test"
  # This is used in order to hide keyboard
- tapOn:
    id: "tabList--notifications"
- assertVisible:
    text: "(?i).*test.*"
    index: 1

