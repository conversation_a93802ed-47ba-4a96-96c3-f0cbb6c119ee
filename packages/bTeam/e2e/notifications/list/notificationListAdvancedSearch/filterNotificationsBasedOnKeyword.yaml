# Scenario: Search notificationList with keyword in adv search screen
#   In NotificationList screen, tap adv search icon to navigate to adv search screen
#   Type keyword and apply changes
#   Verify that matching notifications appear in the results
#   AssertVisible that the keyword appears at least twice. One in searchInput and one more in the list

# In inbox screen press adc criteria button
# Filter messages with specific keyword
# Expect this word to be in inbox screen

# filterNotificationsBasedOnKeyword.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "tabList--notifications"
- tapOn:
    id: "advancedSearchCriteriaButton"
- tapOn:
    id: "notificationsSearchFilters__fields--searchText"
- inputText: "test"
- tapOn:
    id: "notificationsSearchFilters--applyButton"
- assertVisible:
    text: "(?i).*test.*"
    index: 1

