# Scenario: Filter starred notifications from adv search screen
#   In NotificationList screen, tap adv search to navigate to adv search screen
#   Select starred filter
#   Expect the first two elements of the list to be starred.

# filterNotificationsStarred.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../../login/login.yaml
- tapOn:
    id: "tabList--notifications"
- assertVisible:
    id: "notificationList--listItem-0"
- assertVisible:
    id: "notificationList--listItem-1"
- tapOn:
    id: "advancedSearchCriteriaButton"
- swipe:
    start: "46%,68%"
    end: "48%,26%"
    duration: 847
- tapOn:
    id: "notificationsSearchFilters__fields--isStarred"
- tapOn:
    id: "bottomSheetOption-Yes"
- tapOn:
    id: "notificationsSearchFilters--applyButton"
- assertVisible:
    id: "notificationList__listItem-starred-0"
- assertVisible:
    id: "notificationList__listItem-starred-1"


