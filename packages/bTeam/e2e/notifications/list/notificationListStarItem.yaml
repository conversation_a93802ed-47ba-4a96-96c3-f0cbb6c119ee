# Scenario: In notification list screen star item
#   In notification list screen press star icon in one item
#   Expect item to be starred

# notificationListStarItem.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../login/login.yaml
- tapOn:
    id: "tabList--notifications"
- assertVisible:
    id: "notificationList__listItem-unStarred-0"
- tapOn:
    id: "notificationList__listItem-unStarred-0"
- assertVisible:
    id: "notificationList__listItem-starred-0"

