# Scenario: Initialize contacts (only once)
#   Tap in contacts tab
#   Press continue in modal to initialize contacts for the first time
#   Check we have at least one element in the list

# initializeContacts.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
env:
  SEARCH_TEXT: test
---

- runFlow:
    file: ../../login/login.yaml
- tapOn:
    id: "tabList--contacts"
- assertVisible: "Sync Data"
- assertVisible:
    id: "contactList__modal--subtitle"
- tapOn:
    id: "contactList__modal--continueButton"
# - assertVisible:
#     id: "contactList__modal--loading"
# - assertVisible:
#     id: "contactList--listItem-0-Companies"


