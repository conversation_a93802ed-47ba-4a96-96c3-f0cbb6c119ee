# Scenario: Search contactList with keyword
#   In ContactList screen, type a keyword in the search input
#   Verify that matching contacts appear in the results
#   AssertVisible that the keyword appears at least twice. One in searchInput and one more in the list

# searchContactsWithKeyword.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
env:
  SEARCH_TEXT: test
---

- runFlow:
    file: ./initializeContacts.yaml
- tapOn:
    id: "tabList--contacts"
- tapOn:
    id: "contactList--searchInput"
- inputText: "test"
  # This is used in order to hide keyboard
- tapOn:
    id: "contactList--headerText"
- assertVisible:
    text: "(?i).*test.*"
    index: 1


