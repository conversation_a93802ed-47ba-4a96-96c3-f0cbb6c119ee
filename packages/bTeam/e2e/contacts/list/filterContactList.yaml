# Scenario: Filter contacts in ContactList
#   When I select a filter
#   Then only contacts matching the filter are shown
#   Do that for all filters

# filterContactList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ./initializeContacts.yaml
- tapOn:
    id: "tabList--contacts"
- tapOn:
    id: "contactList__searchControls--persons"
- assertVisible:
    id: "contactList--listItem-0-Persons"
- tapOn:
    id: "contactList__searchControls--persons"
- tapOn:
    id: "contactList__searchControls--companies"
- assertVisible:
    id: "contactList--listItem-0-Companies"
- tapOn:
    id: "contactList__searchControls--companies"
- tapOn:
    id: "contactList__searchControls--departments"
- assertVisible:
    id: "contactList--listItem-0-Departments"
- tapOn:
    id: "contactList__searchControls--departments"
- tapOn:
    id: "contactList__searchControls--vessels"
- assertVisible:
    id: "contactList--listItem-0-V<PERSON><PERSON>"


