# Scenario: Filter contacts in ContactList
#   When I select a filter
#   Then only contacts matching the filter are shown
#   Do that for all filters

# filterContactList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../list/initializeContacts.yaml

- tapOn:
    id: "contactList__searchControls--companies"
- tapOn:
    id: "contactList--listItem-0-Companies"
- assertVisible:
    text: "Test Company 1"
    index: 0
- assertVisible: "Company Info"
- assertVisible: "Dept Name:"
- assertVisible: "Contacts:"
- assertVisible: "Address:"
- assertVisible: "Company:"
- assertVisible: "Classifications"
- assertVisible: "Phone:"
- assertVisible: "Fax:"
- assertVisible: "Tlx:"
- assertVisible: "Email:"
- "scroll"
- assertVisible: "Notes:"
- tapOn:
    id: "departmentTab"
- assertVisible: "No data"
- tapOn:
    id: "companyTab"
- tapOn: ""
- assertVisible:
    id: "compose__fields--from"
