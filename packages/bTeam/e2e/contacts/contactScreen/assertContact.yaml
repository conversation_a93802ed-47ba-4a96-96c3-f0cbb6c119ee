# Scenario: Filter contacts in ContactList
#   When I select a filter
#   Then only contacts matching the filter are shown
#   Do that for all filters

# filterContactList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../list/initializeContacts.yaml
- tapOn:
    id: "contactList__searchControls--persons"
- tapOn:
    id: "contactList--listItem-0-Persons"
- assertVisible: "Contact Info"
- assertVisible: "Name:"
- assertVisible: "Title:"
- assertVisible: "Company:"
- assertVisible: "Dept:"
- assertVisible: "Address:"
- assertVisible: "Classifications"
- assertVisible: "Phone:"
- assertVisible: "Fax:"
- assertVisible: "Tlx:"
- assertVisible: "Email:"
- scroll
- assertVisible: "Notes:"
