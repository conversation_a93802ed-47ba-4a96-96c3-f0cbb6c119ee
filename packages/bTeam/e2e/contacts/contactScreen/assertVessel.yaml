# Scenario: Filter contacts in ContactList
#   When I select a filter
#   Then only contacts matching the filter are shown
#   Do that for all filters

# filterContactList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---
- runFlow:
    file: ../list/initializeContacts.yaml

- tapOn:
    id: "contactList__searchControls--vessels"
- tapOn:
    id: "contactList--listItem-0-Vessels"
- assertVisible: "Vessel Info"
- assertVisible: "Address:"
- assertVisible: "Company:"
- assertVisible: "Classifications"
- assertVisible: "Phone:"
- assertVisible: "Fax:"
- assertVisible: "Tlx:"
- assertVisible: "Email:"
- assertVisible: "Notes:"
- tapOn: ""
- assertVisible:
    id: "com.google.android.dialer:id/digits"
