# Filter messages to show only those with no attachments.
# Select a message
# Navigate to attachments tab.
# Check for empty attachments message

# viewMessageAttachmentEmptyList.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "advancedSearchCriteriaButton"
- swipe:
    start: "50%,69%"
    end: "45%,30%"
    duration: 890
- tapOn:
    id: "inboxSearchFilters__fields--hasAttachments"
- tapOn:
    id: "bottomSheetOption-No"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- tapOn:
    id: "messageList--item-0"
    index: 0
- tapOn:
    id: "message__attachmentTab--tabBarIcon"
- assertVisible:
    id: "message__attachmentTab--emptyMessage"
