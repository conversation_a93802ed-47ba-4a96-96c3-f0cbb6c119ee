# Press the first message.
# Swipe right from the first message (no previous message exists)
# Expect to see the same subject.

# messageSwipeNavigationRightFirstMessage.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "messageList--item-0"
    index: 0
- copyTextFrom:
    id: "message--subject"
- evalScript: ${output.messageSubjectSecond = maestro.copiedText}
- swipe:
    direction: "RIGHT"
    timeout: 5000
- copyTextFrom:
    id: "message--subject"
- evalScript: ${output.messageSubjectFirst = maestro.copiedText}
- assertTrue: ${output.messageSubjectFirst == output.messageSubjectSecond}
