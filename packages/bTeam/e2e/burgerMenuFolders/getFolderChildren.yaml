# Open burger menu
# Expand folder
# Verify its children are visible

# getFolderChildren.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../../login/login.yaml
- tapOn:
      id: "inbox__header--burgerIcon"
- assertVisible:
      id: "folderItem--notExpandedButton-6"
- assertNotVisible:
      id: "folderItem--child-parent6-child2-level1"
- tapOn:
      id: "folderItem--notExpandedButton-6"
      retryTapIfNoChange: false # With this we avoid double tap
- assertVisible:
      id: "folderItem--expandedButton-6"
- assertVisible:
      id: "folderItem--child-parent6-child2-level1"
