# In inbox screen press adc criteria button
# Filter messages without attachments only
# Expect first two messages to not have attachment icon

# filterMessagesWithoutAttachments.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--hasAttachments"
- tapOn:
    id: "bottomSheetOption-No"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertNotVisible:
    id: "0-messageList__bottomIcons--hasAttachments"
- assertNotVisible:
    id: "1-messageList__bottomIcons--hasAttachments"
