# In inbox screen press adc criteria button
# Filter read messages only
# Expect first two messages to be read

# filterMessagesRead.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--readUnread"
- tapOn:
    id: "bottomSheetOption-Read"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertVisible:
    id: "0-messageList--usernameNotBoldText"
- assertVisible:
    id: "1-messageList--usernameNotBoldText"
