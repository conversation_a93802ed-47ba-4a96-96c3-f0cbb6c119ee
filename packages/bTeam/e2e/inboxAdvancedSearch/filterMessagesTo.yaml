# In inbox screen press adc criteria button
# Filter messages with specific recipient
# Navigate to first message
# Expect recipient to be correct

# filterMessagesTo.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--to"
- inputText: "<EMAIL>"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- tapOn:
    id: "messageList--item-0"
    index: 0
- assertVisible: ".*<EMAIL>.*"
