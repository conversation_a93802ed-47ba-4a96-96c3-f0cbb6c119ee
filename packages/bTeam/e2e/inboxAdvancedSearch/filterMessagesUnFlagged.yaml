# In inbox screen press adc criteria button
# Filter unFlagged messages only
# Expect first two messages to be unFlagged

# filterMessagesUnFlagged.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: advancedSearchCriteriaButton
- swipe:
    start: 56%,71%
    end: 52%,35%
    duration: 473
- tapOn:
    id: "inboxSearchFilters__fields--flagged"
- tapOn:
    id: "bottomSheetOption-No"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- assertNotVisible:
    id: "0-messageList--flagIcon"
- assertNotVisible:
    id: "1-messageList--flagIcon"
- assertVisible:
    id: "0-messageList--unFlagIcon"
- assertVisible:
    id: "1-messageList--unFlagIcon"
