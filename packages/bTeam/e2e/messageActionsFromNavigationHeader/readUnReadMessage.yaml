# Select a message
# After selection a message by default it becomes read so expect to see read icon
# Press read icon
# Expect to see unRead instead of read icon
# Press unRead icon
# Expect to see read instead of unRead icon

# readUnReadMessage.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "messageList--item-0"
    index: 0
- assertVisible:
    id: "message__navigationHeader--viewedIcon"
- tapOn:
    id: "message__navigationHeader--viewedIcon"
- assertVisible:
    id: "message__navigationHeader--notViewedIcon"
- tapOn:
    id: "message__navigationHeader--notViewedIcon"
- assertVisible:
    id: "message__navigationHeader--viewedIcon"
