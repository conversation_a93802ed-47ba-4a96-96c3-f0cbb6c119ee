# Filter messages to show only flagged
# Select a message
# Press unFlag icon
# Expect to see flag instead of unFlag icon

# unFlagMessage.yaml

appId: gr.benefit.bseafarer
#appId: gr.benefit.bTeam
---

- runFlow:
    file: ../login/login.yaml
- tapOn:
    id: "advancedSearchCriteriaButton"
- swipe:
    start: "50%,69%"
    end: "45%,30%"
    duration: 890
- tapOn:
    id: "inboxSearchFilters__fields--flagged"
- tapOn:
    id: "bottomSheetOption-Yes"
- tapOn:
    id: "inboxSearchFilters--applyButton"
- tapOn:
    id: "messageList--item-0"
    index: 0
- assertVisible:
    id: "message__navigationHeader--flaggedIcon"
- tapOn:
    id: "message__navigationHeader--flaggedIcon"
- assertVisible:
    id: "message__navigationHeader--notFlaggedIcon"
