# Contacts Data Sync

## What is Contacts Sync?

The Contacts Sync system ensures that all contact information in the mobile application stays up-to-date with the central server database. **All sync requests happen in the background, so users can continue using the app without any interruption or blocking.**

## What Gets Synchronized?

The system handles these contact-related entities:

- **Country**
- **City**
- **Classification**
- **ContactType**
- **ContactingType**
- **VesselType**
- **Contact**
- **Address**
- **ContactClassification**
- **Contacting**

### Entity Relationships

- **Core Contacts**: The actual people, companies, vessels, and departments
- **Reference Data**: Countries, cities, contact types, and classifications that support core data
- **Communication Info**: Addresses, phone numbers, emails, and communication preferences
- **Linking Data**: Relationships between contacts and their classifications or categories

## How It Works (User Perspective)

### First-Time Experience

1. **Initial Access** - User taps on Contacts tab for the first time
2. **Sync Permission** - Modal dialog asks user "Do you want to sync contacts data?"
3. **If User Confirms**:
    - Loading screen appears with progress bar
    - All contact entities are downloaded from server
    - Progress bar shows sync completion status
4. **Completion** - User sees the populated contacts list

### Automatic Refresh (Once Per App Session)

1. **App Launch/Login** - User opens app or logs in
2. **First Contacts Visit** - User taps Contacts tab for the first time in this session
3. **Auto-Refresh** - System automatically fetches latest data (no modal)
4. **Visual Feedback** - Pull-to-refresh loader appears in the list
5. **Updated List** - Fresh contact data is displayed

### Subsequent Visits (Same App Session)

1. **Tab Switching** - User switches between tabs and returns to Contacts
2. **No Auto-Refresh** - System shows existing contact list immediately
3. **Manual Control** - User can pull-to-refresh manually if desired
4. **Sync Timestamp** - Date/time of last sync always visible

## Sync Operation Sequence Flow

### 1️⃣ FIRST TIME ACCESS
```
User taps "Contacts" tab (first time ever)
           ↓
Modal appears: "Do you want to sync contacts data?"
           ↓
User selects "Yes"
           ↓
Progress bar appears showing sync progress
           ↓
System downloads all 10 entity types from server
           ↓
Contact list appears with all data
           ↓
Sync timestamp displayed ("Last sync: [Date/Time]")
```

### 2️⃣ FIRST VISIT PER APP SESSION
```
User opens app / logs in
           ↓
User taps "Contacts" tab (first time this session)
           ↓
Automatic refresh starts (no modal)
           ↓
Pull-to-refresh loader appears in list
           ↓  
System fetches updated contact data
           ↓
Updated contact list displayed
           ↓
Sync timestamp updated with new date/time
```

### 3️⃣ SUBSEQUENT VISITS (SAME SESSION)
```
User switches tabs and returns to "Contacts"
           ↓
No automatic refresh occurs
           ↓
User sees existing contact list immediately
           ↓
User can manually pull-to-refresh if desired
           ↓
Sync timestamp always visible in list and menu tab
```

### Visual Summary
| Scenario | Modal | Auto-Refresh | User Action Required |
|----------|-------|--------------|---------------------|
| **First time ever** | ✅ Yes | ❌ No | Confirm sync permission |
| **First visit per app session** | ❌ No | ✅ Yes | None - automatic |
| **Same session visits** | ❌ No | ❌ No | Manual pull-to-refresh only |

### App Session Examples
- **Example 1**: Open app → Go to Contacts (auto-refresh) → Switch to Messages → Return to Contacts (no auto-refresh)
- **Example 2**: Close app → Reopen app → Go to Contacts (auto-refresh again)
- **Example 3**: Logout → Login → Go to Contacts (auto-refresh again)

## Key Features

### Smart Synchronization

- **Only downloads data that has changed since last sync**
- Handles large contact databases efficiently

### User Experience

- **Non-disruptive - Sync runs in background, doesn't block user actions**
- **Transparent** - Clear status indicators show sync progress
- **Fast** - Optimized for quick completion even with thousands of contacts
