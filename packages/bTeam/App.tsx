import React from "react";
import { StyleSheet, View } from "react-native";
import RootNavigation from "./src/navigation/RootNavigation";
import { type Theme, useThemeAwareObject } from "b-ui-lib";
import NetworkStatusBar from "./src/components/networkStatusBar";

const App = () => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.root}>
      <NetworkStatusBar />
      <RootNavigation />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    root: {
      flex: 1,
    },
  });

  return { styles, color };
};

export default App;
