import React, { RefObject } from "react";
import {
  Keyboard,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import {
  type Theme,
  SearchInput,
  SPACING,
  useThemeAwareObject,
} from "b-ui-lib";
import { TEST_IDS } from "../../constants/testIds";
import ContactFilterIcon from "../../components/contactList/contactFilterIcon";
import { CONTACT_TYPES } from "../../constants/contacts";
import { useDispatch, useSelector } from "react-redux";
import { setContactsSearchFilters } from "../../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../../constants/searchEntities";

type Props = {
  searchTextValue: string;
  searchInputRef: RefObject<TextInput>;
  handleSearchInputClear: () => void;
  handleSearchInputChange: (inputValue: string) => void;
  filteredSuggestions: string[];
  handleSuggestionPress: (suggestion: string) => void;
};

const ContactListSearchControls = ({
  searchTextValue,
  searchInputRef,
  handleSearchInputClear,
  handleSearchInputChange,
  filteredSuggestions,
  handleSuggestionPress,
}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  const dispatch = useDispatch();

  const { searchFilters } = useSelector(
    (state: any) =>
      state.root.bTeamSearchFiltersSlice?.[SEARCH_ENTITIES.contacts]
  );

  const setContactsSearchFiltersAction = (payload: { searchFilter: number }) =>
    dispatch(setContactsSearchFilters(payload));

  const onOutsidePress = () => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }
  };

  const isSearchFilterActive = (
    searchFilters: number[],
    searchFilter: number
  ) => {
    return searchFilters.includes(searchFilter);
  };

  const handleSearchFilterPress = (searchFilter: number) => {
    setContactsSearchFiltersAction({
      searchFilter: searchFilter,
    });
  };

  return (
    <TouchableWithoutFeedback onPress={onOutsidePress}>
      <View style={styles.container}>
        <SearchInput
          testID={TEST_IDS.contactListSearchInput}
          searchInputRef={searchInputRef}
          value={searchTextValue}
          containerStyle={styles.searchInput}
          onChangeText={handleSearchInputChange}
          handleInputClear={handleSearchInputClear}
          placeholder={"Search here"}
          suggestions={filteredSuggestions}
          handleSuggestionPress={handleSuggestionPress}
        />

        <View style={styles.searchFilterContainer}>
          <ContactFilterIcon
            testID={TEST_IDS.contactListSearchControlsPersons}
            contactType={CONTACT_TYPES.person}
            isSelected={isSearchFilterActive(
              searchFilters,
              CONTACT_TYPES.person
            )}
            onPress={() => handleSearchFilterPress(CONTACT_TYPES.person)}
          />
          <ContactFilterIcon
            testID={TEST_IDS.contactListSearchControlsCompanies}
            contactType={CONTACT_TYPES.company}
            isSelected={isSearchFilterActive(
              searchFilters,
              CONTACT_TYPES.company
            )}
            onPress={() => handleSearchFilterPress(CONTACT_TYPES.company)}
          />
          <ContactFilterIcon
            testID={TEST_IDS.contactListSearchControlsDepartments}
            contactType={CONTACT_TYPES.department}
            isSelected={isSearchFilterActive(
              searchFilters,
              CONTACT_TYPES.department
            )}
            onPress={() => handleSearchFilterPress(CONTACT_TYPES.department)}
          />
          <ContactFilterIcon
            testID={TEST_IDS.contactListSearchControlsVessels}
            contactType={CONTACT_TYPES.vessel}
            isSelected={isSearchFilterActive(
              searchFilters,
              CONTACT_TYPES.vessel
            )}
            onPress={() => handleSearchFilterPress(CONTACT_TYPES.vessel)}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ContactListSearchControls;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    searchInput: {
      marginVertical: SPACING.TEN,
      marginHorizontal: SPACING.FOURTEEN,
      zIndex: 1000,
    },
    searchFilterContainer: {
      flexDirection: "row",
      alignItems: "center",
      margin: SPACING.TEN,
      gap: SPACING.SIX,
    },
  });

  return { styles, color };
};
