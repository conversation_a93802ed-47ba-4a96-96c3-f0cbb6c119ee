import { FolderDTO } from "../types/DTOs/FolderDTO";
import { Folder } from "../types/folder";
import {
  SEARCH_RESULTS_FOLDER_ID, SEARCH_RESULTS_FOLDER_NAME,
} from "../constants/searchResultsFolder";

const IS_SEARCH_ITEM = 3;

export const mapFolder = (folder: FolderDTO, oldFolder: Folder): Folder => ({
  id: folder.FLD_Guid,
  parentFolderId: folder.FLD_FLD_Guid || null,
  folderCriteriaId: folder.FLD_CRC_Guid || null,
  name: folder.FLD_Name,
  level: folder.FLD_Level || null,
  sortOrder: folder.FLD_SortOrder || null,
  isVisible: folder.FLD_IsVisible || null,
  createdBy: folder.FLD_CreatedUserGuid,
  createdAt: new Date(folder.FLD_CreatedTimestamp),
  updatedBy: folder.FLD_UpdatedUserGuid,
  updatedAt: new Date(folder.FLD_UpdatedTimestamp),
  deletedBy: folder.FLD_DeletedUserGuid || null,
  status: folder.RecordStatus,
  isExpanded: false,
  isSearchItem: folder.FLD_FLT_Id === IS_SEARCH_ITEM,
  isLoading: false,
  emailsCount: folder.FLD_Count || null,
  emailIds: oldFolder?.emailIds ? oldFolder.emailIds : [],
  hasChildren: folder.HasChildren || false,
  childrenIds: oldFolder?.childrenIds ? oldFolder.childrenIds : [],
  searchResultsFolderIds: [],
});

export const mapSearchResultsFolder = (searchResultsFolderIds: string[]): Folder => ({
  id: SEARCH_RESULTS_FOLDER_ID,
  name: SEARCH_RESULTS_FOLDER_NAME,
  parentFolderId: null,
  folderCriteriaId: null,
  level: null,
  sortOrder: null,
  isVisible: null,
  createdBy: '',
  createdAt: new Date(),
  updatedBy: '',
  updatedAt: new Date(),
  deletedBy: null,
  status: 0,
  isExpanded: false,
  isSearchItem: true,
  isLoading: false,
  emailsCount: 0,
  emailIds: [],
  hasChildren: false,
  childrenIds: [],
  searchResultsFolderIds: searchResultsFolderIds || [],
});
