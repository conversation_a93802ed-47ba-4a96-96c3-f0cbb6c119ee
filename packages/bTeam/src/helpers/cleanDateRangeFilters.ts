import { NOTIFICATIONS_SEARCH_FIELD_NAMES } from "../constants/notificationsSearchFields";

// Although CreateDateFrom and CreateDateTo are separate filters in the API, we treat them as a single filter when both are set.
// Therefore, we remove one of both to count them as one.
export const filterNotificationSearchFilters = <T extends Record<string, any>>(
  filters: T
): T => {
  const hasStartDate =
    !!filters[NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom];
  const hasEndDate = !!filters[NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateTo];

  // If both dates are present, remove createDateTo
  if (hasStartDate && hasEndDate) {
    const {
      [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateTo]: _,
      ...filtersWithoutEndDate
    } = filters;
    return { ...filtersWithoutEndDate } as T;
  }

  // If only one date is present or none, return the original filters
  return { ...filters };
};
