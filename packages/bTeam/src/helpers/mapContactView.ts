import { Contact } from "../types/Contact";
import { ContactView } from "../types/contactView";
import { calculateAvatarName } from "./calculateAvatarName";
import { CONTACT_TYPES } from "../constants/contacts";

type ContactViewParams = {
  contactsById: Record<string, Contact>;
  contact: Contact;
};

const calculateRelatedContactDisplayName = ({
  contact,
  contactsById,
}: ContactViewParams): string => {
  // For now, we want to show subtitle (company name) only for departments
  if (contact?.CNT_CNT_Guid && contact?.CNT_Type === CONTACT_TYPES.department) {
    const relatedContact = contactsById[contact.CNT_CNT_Guid];
    if (relatedContact) {
      return relatedContact.CNT_DisplayName || "";
    }
  }

  return "";
};

export const mapContactView = ({
  contactsById,
  contact,
}: ContactViewParams): ContactView => {
  const subtitle = calculateRelatedContactDisplayName({
    contact,
    contactsById,
  });

  return {
    id: contact.CNT_Guid,
    avatarText: calculateAvatarName(contact.CNT_DisplayName),
    title: contact.CNT_DisplayName,
    subtitle,
    contactType: contact.CNT_Type,
  };
};
