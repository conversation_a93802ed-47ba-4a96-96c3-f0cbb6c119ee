/**
 * Calculates keyword suggestions based on search input and available suggestions
 * @param searchText - The current search text input
 * @param searchSuggestions - Array of available search suggestions
 * @returns Filtered and limited array of suggestions
 */
export const calculateKeywordSuggestions = (
  searchText: string | undefined,
  searchSuggestions: string[]
): string[] => {
  if (!searchSuggestions || searchSuggestions.length === 0) {
    return [];
  }

  // If no search text, return first three suggestions
  if (!searchText || searchText.trim() === '') {
    return searchSuggestions.slice(0, 3);
  }

  const searchTerm = searchText.toLowerCase();

  return searchSuggestions.reduce((matches: string[], suggestion: string) => {
    // Stop once we have 3 matches
    if (matches.length >= 3) return matches;

    const lowerSuggestion = suggestion.toLowerCase();
    // Add if it includes searchTerm but isn't exactly the same
    if (
      lowerSuggestion.includes(searchTerm) &&
      lowerSuggestion !== searchTerm
    ) {
      matches.push(suggestion);
    }

    return matches;
  }, []);
};
