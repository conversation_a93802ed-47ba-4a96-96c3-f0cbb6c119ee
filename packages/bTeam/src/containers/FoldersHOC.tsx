import React, { useEffect, useState } from "react";
import FoldersScreen from "../components/folders/FoldersScreen";
import { useDispatch, useSelector } from "react-redux";
import {
  copyEmailsToFolder,
  getFolderChildren,
  moveEmailsToFolder,
} from "../slices/gridMessageSlice";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import {
  createChildrenHierarchy,
  FOLDERS_NAMES,
} from "../helpers/createChildrenHierarchy";
import { cancelMultiSelection } from "../slices/generalSlice";
import { EMAIL_CASES } from "../constants/emailCases";

const FoldersHOC: React.FC = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { folders, selectedFolderId } = useSelector(
    (state) => state.persist.gridMessageSlice
  );
  const { userName } = useSelector((state) => state.persist.bTeamAuth);
  const { selectedMessageIds } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );

  // We create a copy of the folders from the state to avoid mutating them when updating the component’s folders
  const [personalFolders, setPersonalFolders] = useState([]);
  const [targetFolderId, setTargetFolderId] = useState("");
  const [expandedFolderIds, setExpandedFolderIds] = useState<string[]>([]);
  const route = useRoute();
  const { emailCase, isMultiSelect, messageId } = route.params;

  const updateIsExpanded = (folder, expandedFolderIds: string[]) => {
    const isExpanded = expandedFolderIds.includes(folder.id);

    return {
      ...folder,
      isExpanded: isExpanded,
      children: folder.children
        ? folder.children.map((child) =>
            updateIsExpanded(child, expandedFolderIds)
          )
        : [],
    };
  };

  const calculatePersonalFolders = (folders, userName, expandedFolderIds) => {
    const foldersHierarchy = createChildrenHierarchy(
      folders.byId,
      folders.rootIds,
      userName,
      true
    );

    const personalFolders = foldersHierarchy.filter(
      (folder) =>
        folder.name !== FOLDERS_NAMES.All &&
        folder.name !== FOLDERS_NAMES.Inbox &&
        folder.name !== FOLDERS_NAMES.Sent &&
        folder.name !== FOLDERS_NAMES.Drafts &&
        folder.name !== FOLDERS_NAMES.Junk &&
        folder.name !== FOLDERS_NAMES.Deleted &&
        folder.name !== FOLDERS_NAMES.FrequentlyUsedFolders
    );

    return personalFolders.map((folder) =>
      updateIsExpanded(folder, expandedFolderIds)
    );
  };

  useEffect(() => {
    setPersonalFolders(
      calculatePersonalFolders(folders, userName, expandedFolderIds)
    );
  }, [folders, expandedFolderIds]);

  const moveToFolderAction = () => {
    if (isMultiSelect) {
      dispatch(cancelMultiSelection());
    }

    dispatch(
      moveEmailsToFolder({
        selectedMessageIds: isMultiSelect ? selectedMessageIds : [messageId],
        selectedFolderId,
        folderDestination: targetFolderId,
        isMultiSelect,
      })
    );
  };

  const copyToFolderAction = () => {
    if (isMultiSelect) {
      dispatch(cancelMultiSelection());
    }

    dispatch(
      copyEmailsToFolder({
        selectedMessageIds: isMultiSelect ? selectedMessageIds : [messageId],
        selectedFolderId,
        folderDestination: targetFolderId,
        isMultiSelect,
      })
    );
  };

  const handleToggleFolderExpand = (folderId: string) => {
    if (!expandedFolderIds.includes(folderId)) {
      dispatch(getFolderChildren({ folderId }));
    }

    setExpandedFolderIds((prev) =>
      expandedFolderIds.includes(folderId)
        ? prev.filter((id) => id !== folderId)
        : [...prev, folderId]
    );
  };

  const action = {
    [EMAIL_CASES.move]: moveToFolderAction,
    [EMAIL_CASES.copy]: copyToFolderAction,
  };

  return (
    <FoldersScreen
      folders={personalFolders}
      selectedFolderId={targetFolderId}
      handleToggleFolderExpand={handleToggleFolderExpand}
      handleSetSelectedFolder={setTargetFolderId}
      navigation={navigation}
      onPress={action[emailCase]}
      emailCase={emailCase}
    />
  );
};

export default FoldersHOC;
