import React, { useState, useEffect, useCallback, useMemo } from "react";
import ContactListScreen from "../components/contactList/ContactListScreen";
import { useDispatch, useSelector } from "react-redux";
import { ContactView } from "../types/contactView";
import { mapContactView } from "../helpers/mapContactView";
import SearchBarAnimationHOC from "./SearchBarAnimationHOC";
import { Platform, Alert, View, StyleSheet, Text } from "react-native";
import ContactListSearchControls from "../navigation/headers/contactListSearchControls";
import { setContactsSearchText } from "../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";
import {
  CONTACT_TYPES,
  CONTACT_TYPE_DISPLAY_NAMES,
} from "../constants/contacts";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { SCREEN_NAMES } from "../constants/screenNames";
import { syncOperation } from "../helpers/syncOperationHelper";
import { setHasEverSyncContacts } from "../slices/syncSlice";
import {
  useThemeAwareObject,
  Theme,
  Modal,
  BUTTON_DEFAULT_VARIANTS,
} from "b-ui-lib";
import { TEST_IDS } from "../constants/testIds";
import SyncProgressBar from "../components/contactList/SyncProgressBar";
import { addSearchSuggestion } from "../slices/searchSuggestionsSlice";
import _lodash from "lodash";
import filterSearchSuggestions from "../helpers/filterSearchSuggestions";

const DEFAULT_HEADER_HEIGHT = Platform.OS === "ios" ? 147 : 157;

const ContactListHOC: React.FC = () => {
  const { byId, companyIds, personIds, vesselIds, departmentIds } = useSelector(
    (state: any) => state.persist.bTeamContactsSlice.contacts
  );

  const {
    countriesIsLoading,
    citiesIsLoading,
    classificationsIsLoading,
    contactTypesIsLoading,
    contactingTypesIsLoading,
    vesselTypesIsLoading,
    contactsIsLoading,
    addressesIsLoading,
    contactClassificationsIsLoading,
    contactingIsLoading,
    contactsSyncCompletedCount,
  } = useSelector((state: any) => state.persist.bTeamContactsSlice);

  const { hasEverSyncContacts, syncIsLoading } = useSelector(
    (state: any) => state.persist.bTeamSyncSlice
  );

  const { token, domainBaseUrl, user } = useSelector(
    (state: any) => state.persist.bTeamAuth
  );

  const { searchFilters, searchText } = useSelector(
    (state: any) =>
      state.root.bTeamSearchFiltersSlice?.[SEARCH_ENTITIES.contacts]
  );

  const { searchSuggestions } = useSelector(
    (state) =>
      state.persist.bTeamSearchSuggestionsSlice?.[SEARCH_ENTITIES.contacts]
  );

  const setContactsSearchTextAction = (payload) =>
    dispatch(setContactsSearchText(payload));

  const setHasEverSyncContactsAction = () => dispatch(setHasEverSyncContacts());

  const addSearchSuggestionAction = (suggestion: string) =>
    dispatch(
      addSearchSuggestion({
        entityType: SEARCH_ENTITIES.contacts,
        suggestion,
      })
    );

  const [showSyncModal, setShowSyncModal] = useState(false);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const { styles } = useThemeAwareObject(createStyles);

  const CONTACTS_SYNC_LOADERS = [
    countriesIsLoading,
    citiesIsLoading,
    classificationsIsLoading,
    contactTypesIsLoading,
    contactingTypesIsLoading,
    vesselTypesIsLoading,
    contactsIsLoading,
    addressesIsLoading,
    contactClassificationsIsLoading,
    contactingIsLoading,
  ];

  const performSyncOperation = async () => {
    try {
      await syncOperation({
        token,
        domainBaseUrl,
        userGuid: user?.USR_Guid,
        dispatch,
      });

      if (!hasEverSyncContacts) {
        setHasEverSyncContactsAction();
      }
    } catch (error) {
      Alert.alert(
        `Sync Failed: ${error}`,
        "Failed to sync contacts. Please try again."
      );
    } finally {
      // Auto-close modal after sync completes
      setShowSyncModal(false);
    }
  };

  const handleCloseSyncModal = () => setShowSyncModal(false);

  // If the user has never synced contacts, show the sync modal
  useFocusEffect(
    useCallback(() => {
      if (!hasEverSyncContacts) {
        setShowSyncModal(true);
      }
    }, [hasEverSyncContacts])
  );

  useEffect(() => {
    if (hasEverSyncContacts) {
      performSyncOperation();
    }
  }, []);

  const processContactsData = (
    byId: any,
    companyIds: string[],
    personIds: string[],
    vesselIds: string[],
    departmentIds: string[],
    searchText: string,
    searchFilters: number[]
  ): ContactView[] => {
    // Aggregate contact IDs based on search filters
    // If searchFilters is empty, include all contact types
    const CONTACT_TYPE_TO_IDS = {
      [CONTACT_TYPES.company]: companyIds,
      [CONTACT_TYPES.person]: personIds,
      [CONTACT_TYPES.vessel]: vesselIds,
      [CONTACT_TYPES.department]: departmentIds,
    };

    const allContactIds =
      searchFilters.length === 0
        ? Object.values(CONTACT_TYPE_TO_IDS).flat()
        : searchFilters.flatMap((filter) => CONTACT_TYPE_TO_IDS[filter] || []);

    return allContactIds.reduce<ContactView[]>((acc, id) => {
      const contact = byId[id];

      if (!contact) return acc;

      // Skip if contact's display name doesn't match search text
      if (
        searchText?.trim() &&
        !(contact.CNT_DisplayName || "")
          .toLowerCase()
          .includes(searchText.trim().toLowerCase())
      ) {
        return acc;
      }

      acc.push(
        mapContactView({
          contactsById: byId,
          contact,
        })
      );

      return acc;
    }, []);
  };

  const generateListTitle = (searchFilters: number[]) => {
    if (searchFilters.length === 0) return "All";

    const displayNames = searchFilters.map(
      (filter) => CONTACT_TYPE_DISPLAY_NAMES[filter]
    );

    if (displayNames.length === 1) return displayNames[0];

    return displayNames.join(" & ");
  };

  const handleSearchInputClear = () => {
    setContactsSearchTextAction({ searchText: "" });
  };

  const debouncedAddSearchSuggestion = useCallback(
    _lodash.debounce(
      (suggestion: string, otherDebounceParams?: {}) =>
        addSearchSuggestionAction(suggestion),
      500
    ),
    []
  );

  const handleSearchInputChange = (textValue: string) => {
    setContactsSearchTextAction({ searchText: textValue });

    if (textValue.trim()) {
      debouncedAddSearchSuggestion(textValue);
    }
  };

  const contactsData = processContactsData(
    byId,
    companyIds,
    personIds,
    vesselIds,
    departmentIds,
    searchText,
    searchFilters
  );

  const handleTapContact = (contactId: string) => {
    const navDictionary = {
      1: SCREEN_NAMES.generalContactDetails,
      2: SCREEN_NAMES.contactDetails,
      4: SCREEN_NAMES.vesselDetails,
      8: SCREEN_NAMES.departmentDetails,
    };
    const contactType = byId[contactId]?.CNT_Type;

    return navigation.navigate(navDictionary[contactType], {
      contactGuid: contactId,
    });
  };

  const MODAL_BUTTONS = [
    {
      title: "Continue",
      onPress: performSyncOperation,
      variant: BUTTON_DEFAULT_VARIANTS.primary,
      testID: TEST_IDS.contactListModalContinueButton,
    },
    {
      title: "Cancel",
      onPress: handleCloseSyncModal,
      variant: BUTTON_DEFAULT_VARIANTS.secondary,
      testID: TEST_IDS.contactListModalCancelButton,
    },
  ];

  const filteredSuggestions = useMemo(
    () => filterSearchSuggestions(searchSuggestions, searchText),
    [searchSuggestions]
  );

  const handleSuggestionPress = (suggestion: string) => {
    setContactsSearchTextAction({ searchText: suggestion });
  };

  return (
    <View style={styles.container}>
      <Modal
        isVisible={showSyncModal}
        handleClose={syncIsLoading ? () => {} : handleCloseSyncModal}
        title={!syncIsLoading ? "Sync Data" : undefined}
        subtitle={
          !syncIsLoading
            ? "Would you like to sync data for contacts, this may take some time"
            : undefined
        }
        buttonsArray={!syncIsLoading ? MODAL_BUTTONS : undefined}
        testIDTitle={TEST_IDS.contactListModalTitle}
        testIDSubtitle={TEST_IDS.contactListModalSubtitle}
      >
        <SyncProgressBar
          completedCount={contactsSyncCompletedCount}
          totalCount={CONTACTS_SYNC_LOADERS.length}
          isVisible={syncIsLoading}
        />
      </Modal>

      <SearchBarAnimationHOC
        headerHeight={DEFAULT_HEADER_HEIGHT}
        searchBar={
          <ContactListSearchControls
            searchTextValue={searchText}
            searchInputRef={React.createRef()}
            handleSearchInputClear={handleSearchInputClear}
            handleSearchInputChange={handleSearchInputChange}
            filteredSuggestions={filteredSuggestions}
            handleSuggestionPress={handleSuggestionPress}
          />
        }
      >
        <ContactListScreen
          contactsData={contactsData}
          listTitle={generateListTitle(searchFilters)}
          emptyListMessage={
            !hasEverSyncContacts
              ? "No contacts has synced"
              : "No contacts found"
          }
          isLoading={!hasEverSyncContacts ? false : syncIsLoading}
          handleRefreshList={performSyncOperation}
          handleTapContact={handleTapContact}
        />
      </SearchBarAnimationHOC>
    </View>
  );
};

export default ContactListHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
  });

  return { styles, color };
};
