import {
  getAddresses,
  getAddressesFailed,
  getAddressesSuccess,
  getCities,
  getCitiesFailed,
  getCitiesSuccess,
  getClassifications,
  getClassificationsFailed,
  getClassificationsSuccess,
  getContactClassifications,
  getContactClassificationsFailed,
  getContactClassificationsSuccess,
  getC<PERSON>acting,
  getContactingFailed,
  getC<PERSON><PERSON>ingSuccess,
  getContactingTypes,
  getContactingTypesFailed,
  getContactingTypesSuccess,
  getContacts,
  getContactsFailed,
  getContactsSuccess,
  getContactTypes,
  getContactTypesFailed,
  getContactTypesSuccess,
  getCountries,
  getCountriesFailed,
  getCountriesSuccess,
  getVesselTypes,
  getVesselTypesFailed,
  getVesselTypesSuccess,
} from "../slices/contactsSlice";
import {SYNC_CONTACTS_ENTITIES_NAMES} from "./syncEntities";

export const CONTACT_TYPES = {
  company: 1,
  person: 2,
  vessel: 4,
  department: 8,
  branchCompany: 16,
} as const;

export const CONTACT_ICONS = {
  [CONTACT_TYPES.person]: "user",
  [CONTACT_TYPES.company]: "anchor",
  [CONTACT_TYPES.vessel]: "cargo-ship",
  [CONTACT_TYPES.department]: "dpt",
};

export const CONTACT_TYPE_DISPLAY_NAMES = {
  [CONTACT_TYPES.company]: "Companies",
  [CONTACT_TYPES.person]: "Persons",
  [CONTACT_TYPES.vessel]: "Vessels",
  [CONTACT_TYPES.department]: "Departments",
} as const;

// Create a mapping of entity names to their corresponding actions
export const CONTACTS_ENTITIES_ACTIONS: Record<
  string,
  {
    getAction: () => any;
    getSuccessAction: (payload: { data: any; isLastPage?: boolean }) => any;
    getFailedAction: (error: any) => any;
  }
> = {
  [SYNC_CONTACTS_ENTITIES_NAMES.country]: {
    getAction: () => getCountries(),
    getSuccessAction: (payload) => getCountriesSuccess(payload),
    getFailedAction: (error) => getCountriesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.city]: {
    getAction: () => getCities(),
    getSuccessAction: (payload) => getCitiesSuccess(payload),
    getFailedAction: (error) => getCitiesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.classification]: {
    getAction: () => getClassifications(),
    getSuccessAction: (payload) => getClassificationsSuccess(payload),
    getFailedAction: (error) => getClassificationsFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contactType]: {
    getAction: () => getContactTypes(),
    getSuccessAction: (payload) => getContactTypesSuccess(payload),
    getFailedAction: (error) => getContactTypesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contactingType]: {
    getAction: () => getContactingTypes(),
    getSuccessAction: (payload) => getContactingTypesSuccess(payload),
    getFailedAction: (error) => getContactingTypesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.vesselType]: {
    getAction: () => getVesselTypes(),
    getSuccessAction: (payload) => getVesselTypesSuccess(payload),
    getFailedAction: (error) => getVesselTypesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contact]: {
    getAction: () => getContacts(),
    getSuccessAction: (payload) => getContactsSuccess(payload),
    getFailedAction: (error) => getContactsFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.address]: {
    getAction: () => getAddresses(),
    getSuccessAction: (payload) => getAddressesSuccess(payload),
    getFailedAction: (error) => getAddressesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contactClassification]: {
    getAction: () => getContactClassifications(),
    getSuccessAction: (payload) => getContactClassificationsSuccess(payload),
    getFailedAction: (error) => getContactClassificationsFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contacting]: {
    getAction: () => getContacting(),
    getSuccessAction: (payload) => getContactingSuccess(payload),
    getFailedAction: (error) => getContactingFailed(error),
  },
};
