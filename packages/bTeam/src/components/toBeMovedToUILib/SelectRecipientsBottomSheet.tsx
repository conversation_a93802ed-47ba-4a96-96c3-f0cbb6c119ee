import React, { type RefObject, useMemo, useState, ReactNode } from "react";
import { ActivityIndicator, Dimensions, StyleSheet, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import {
  Theme,
  useThemeAwareObject,
  SPACING,
  Button,
  SearchInput,
  AvatarEmailItem,
  CustomBottomSheet,
} from "b-ui-lib";
import type { BottomSheetMethods } from "@gorhom/bottom-sheet/lib/typescript/types";
import BottomSheetHeader from "../general/BottomSheetHeader";
import { TEST_IDS } from "../../constants/testIds";

type Props = {
  bottomSheetRef?: RefObject<BottomSheetMethods> | null;
  handleBackPress?: () => boolean;
  handleBackdropPress?: () => void;
  recipientsEmails: any;
  handleAddButton: any;
  selectedRecipientEmailId: string;
  handleSelectRecipientEmail: (emailId: string) => void;
  handleCloseBottomSheet: (emailId: string) => void;
  titleBoldText: string;
  titleNormalText: string;
  fieldToFilterSearch: string;
  children?: ReactNode;
  isLoading?: boolean;
  scrollViewHeight?: number;
};

const SelectRecipientsBottomSheet: React.FC = ({
  bottomSheetRef,
  handleBackPress,
  handleBackdropPress,
  recipientsEmails,
  handleAddButton,
  selectedRecipientEmailId,
  handleSelectRecipientEmail,
  handleCloseBottomSheet,
  titleBoldText,
  titleNormalText,
  fieldToFilterSearch,
  children,
  isLoading,
  scrollViewHeight,
  ...rest
}: Props) => {
  const [searchInputValue, setSearchInputValue] = useState<string>("");
  const screenHeight = Dimensions.get("window").height;
  // We need the scrollViewHeight when this component is rendered inside Tab.Navigator.
  const maxHeightScrollView = useMemo(
    () => (scrollViewHeight ? scrollViewHeight : screenHeight * 0.35),
    [screenHeight, scrollViewHeight]
  );

  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, maxHeightScrollView)
  );

  const filteredRecipientsEmails = useMemo(() => {
    if (!searchInputValue) {
      return recipientsEmails;
    }

    return recipientsEmails.filter((email: any) => {
      if (fieldToFilterSearch) {
        return email?.[fieldToFilterSearch]
          ?.toLowerCase()
          .includes(searchInputValue.toLowerCase());
      }

      return email.value.toLowerCase().includes(searchInputValue.toLowerCase());
    });
  }, [searchInputValue]);

  return (
    <CustomBottomSheet
      {...rest}
      bottomSheetRef={bottomSheetRef}
      handleBackPress={handleBackPress}
      hasBackdrop
      handleBackdropPress={handleBackdropPress}
    >
      <ScrollView style={styles.scrollViewContainer}>
        <View style={styles.container}>
          {isLoading && (
            <View
              style={[
                StyleSheet.absoluteFill,
                { zIndex: 100, backgroundColor: color.GREY_OPACITY },
              ]}
            >
              <ActivityIndicator
                size="large"
                color={color.MESSAGE_FLAG}
                style={{ flex: 1 }}
              />
            </View>
          )}

          {children && Object.keys(children)?.length ? (
            children
          ) : (
            <>
              <BottomSheetHeader
                titleBoldText={titleBoldText}
                titleNormalText={titleNormalText}
                handleCloseBottomSheet={() => handleCloseBottomSheet("")}
              />

              <SearchInput
                testID={TEST_IDS.selectRecipientsBottomSheetSearchInput}
                placeholder="Find a name"
                value={searchInputValue}
                onChangeText={(text) => setSearchInputValue(text)}
                handleInputClear={() => setSearchInputValue("")}
              />

              <View>
                {filteredRecipientsEmails?.map((email: any) => (
                  <AvatarEmailItem
                    key={email.id}
                    email={email}
                    handleSelectEmail={handleSelectRecipientEmail}
                    isSelected={
                      Array.isArray(selectedRecipientEmailId)
                        ? selectedRecipientEmailId.includes(email.id)
                        : email.id === selectedRecipientEmailId
                    }
                  />
                ))}
              </View>
              <Button
                testID={TEST_IDS.selectRecipientsBottomSheetAddButton}
                title="Add"
                onPress={handleAddButton}
              />
            </>
          )}
        </View>
      </ScrollView>
    </CustomBottomSheet>
  );
};

export default SelectRecipientsBottomSheet;

const createStyles = ({ color }: Theme, maxHeightScrollView: number) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BORDER_COLOR_FORM,
      padding: SPACING.M,
      gap: SPACING.XL,
    },
    scrollViewContainer: {
      flex: 1,
    },
  });

  return { styles, color };
};
