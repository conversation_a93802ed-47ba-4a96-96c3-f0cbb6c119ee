import React from "react";
import { StyleSheet, View, Linking } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  IconButton,
  SPACING,
} from "b-ui-lib";
import { useNavigation } from "@react-navigation/native";
import { SCREEN_NAMES } from "../../constants/screenNames";

type Props = {
  label: string;
  value: string | string[] | React.ReactNode | null;
  isBold?: boolean;
  isPhone?: boolean;
  isMail?: boolean;
};

const DetailRow: React.FC<Props> = ({
  label,
  value,
  isBold = false,
  isPhone,
  isMail,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const navigation = useNavigation();

  const handlePhonePress = (phoneNumber: string) => {
    const cleanNumber = phoneNumber.replace(/[^\d+]/g, "");
    Linking.openURL(`tel:${cleanNumber}`);
  };

  const handleMailPress = (email: string) => {
    (navigation as any).navigate(SCREEN_NAMES.composeMessage, {
      prefilledToEmail: email,
    });
  };

  return (
    <View style={styles.row}>
      <CustomText style={styles.label}>{label}</CustomText>

      {Array.isArray(value) && (isPhone || isMail) ? (
        <View style={{ flex: 5, gap: SPACING.XS }}>
          {value.map((item, index) => (
            <View
              style={{
                flexDirection: "row",
                borderBottomColor: color.TASK_DETAILS_BORDER,
                borderBottomWidth: index === value.length - 1 ? 0 : 1,
                paddingBottom: index === value.length - 1 ? 0 : SPACING.M,
                paddingTop: index === 0 ? 0 : SPACING.XS,
                alignItems: "center",
              }}
            >
              <CustomText
                key={index}
                style={[styles.value, isBold && styles.boldValue, {}]}
                canCopy
              >
                {item}
              </CustomText>

              <IconButton
                name={isPhone ? "call" : "mail"}
                size={16}
                color={color.TEXT_DEFAULT}
                onPress={() => {
                  if (isPhone) {
                    handlePhonePress(item);
                  } else if (isMail) {
                    handleMailPress(item);
                  }
                }}
                containerStyle={{
                  padding: 16,
                  backgroundColor: color.BRAND_DEFAULT,
                  borderRadius: 6,
                }}
              />
            </View>
          ))}
        </View>
      ) : (
        <View style={{ flex: 5 }}>
          {typeof value === "string" ? (
            <CustomText
              style={[styles.value, isBold && styles.boldValue]}
              canCopy
            >
              {value}
            </CustomText>
          ) : (
            value
          )}
        </View>
      )}
    </View>
  );
};

export default DetailRow;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    row: {
      flex: 1,
      flexDirection: "row",
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: color.TASK_DETAILS_BORDER,
    },
    label: {
      fontWeight: "600",
      marginRight: 6,
      fontSize: 12,
      flex: 2,
    },
    value: {
      flex: 5,
      fontSize: 12,
    },
    boldValue: {
      fontWeight: "bold",
    },
  });

  return { styles, color };
};
