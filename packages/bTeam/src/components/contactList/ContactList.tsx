import React from "react";
import {
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
  LayoutChangeEvent,
  ViewStyle,
  NativeSyntheticEvent,
  NativeScrollEvent,
  FlatListProps,
} from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
} from "b-ui-lib";
import { ContactView } from "../../types/contactView";
import ContactItem from "./ContactItem";
import Animated from "react-native-reanimated";
import { TEST_IDS } from "../../constants/testIds";
import { CONTACT_TYPE_DISPLAY_NAMES } from "../../constants/contacts";

const AnimatedFlatList =
  Animated.createAnimatedComponent<FlatListProps<ContactView>>(FlatList);

type Props = {
  data: ContactView[];
  title?: string;
  emptyListMessage?: string;
  isLoading?: boolean;
  handleRefreshList: () => void;
  handleTapContact: (contactId: string) => void;

  // FlatList props passed from SearchBarAnimationHOC
  onScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  listStyle?: ViewStyle | ViewStyle[];
  onLayout?: (event: LayoutChangeEvent) => void;
};

const ContactList = ({
  data,
  title,
  emptyListMessage,
  isLoading,
  handleRefreshList,
  handleTapContact,
  onScroll,
  listStyle,
  onLayout,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <AnimatedFlatList
      style={[styles.container, listStyle]}
      onScroll={onScroll}
      onLayout={onLayout}
      data={data}
      ListHeaderComponent={
        title ? (
          <CustomText
            testID={TEST_IDS.contactListHeaderText}
            style={styles.title}
          >
            {title}
          </CustomText>
        ) : null
      }
      refreshControl={
        <RefreshControl
          refreshing={isLoading}
          onRefresh={() => handleRefreshList()}
          tintColor={color.BLACK}
        />
      }
      keyExtractor={(item: ContactView) => item.id}
      showsVerticalScrollIndicator={false}
      renderItem={({ item, index }) => {
        return (
          <ContactItem
            testID={`${TEST_IDS.contactListItem}-${index}-${
              CONTACT_TYPE_DISPLAY_NAMES?.[item?.contactType]
            }`}
            contact={item}
            onPress={handleTapContact}
          />
        );
      }}
      ListEmptyComponent={
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            paddingTop: 70,
          }}
        >
          <CustomText>
            {emptyListMessage ? emptyListMessage : "No Contacts"}
          </CustomText>
        </View>
      }
    />
  );
};

export default ContactList;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    title: {
      padding: SPACING.M,
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.TEXT_SEARCH_INVERTED,
    },
  });

  return { styles, color };
};
