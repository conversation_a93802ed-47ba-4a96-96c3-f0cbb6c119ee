import React, { useState, useEffect } from "react";
import { StyleSheet, View } from "react-native";
import { useThemeAwareObject, Theme, CustomText } from "b-ui-lib";
import { TEST_IDS } from "../../constants/testIds";

interface SyncProgressBarProps {
  completedCount: number;
  totalCount: number;
  isVisible: boolean;
}

const SyncProgressBar: React.FC<SyncProgressBarProps> = ({
  completedCount,
  totalCount,
  isVisible,
}) => {
  const [dots, setDots] = useState("");
  const { styles } = useThemeAwareObject(createStyles);

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev === "") return ".";
        if (prev === ".") return "..";
        if (prev === "..") return "...";
        if (prev === "...") return "";
        return "";
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <View style={styles.progressContainer}>
      <View style={styles.textContainer}>
        <CustomText
          testID={TEST_IDS.contactListModalCancelLoading}
          style={styles.progressText}
        >
          Loading {completedCount || 0} of {totalCount || 0}
        </CustomText>
        <View style={styles.dotsContainer}>
          <CustomText style={styles.dotsText}>{dots || ""}</CustomText>
        </View>
      </View>
    </View>
  );
};

export default SyncProgressBar;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    progressContainer: {
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 20,
    },
    textContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    progressText: {
      fontSize: 16,
      textAlign: "center",
      fontWeight: "500",
    },
    dotsContainer: {
      width: 30,
    },
    dotsText: {
      fontSize: 16,
      textAlign: "left",
    },
  });

  return { styles, color };
};
