import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  SPACING,
  BenefitIconSet,
} from "b-ui-lib";
import { CONTACT_ICONS } from "../../constants/contacts";

type Props = {
  testID: string;
  contactType: number;
  isSelected: boolean;
  onPress: (contactType: string) => void;
};

const ContactFilterIcon = ({
  testID,
  contactType,
  isSelected,
  onPress,
}: Props) => {
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isSelected)
  );

  return (
    <TouchableOpacity
      testID={testID}
      style={styles.container}
      onPress={() => onPress(contactType?.toString())}
    >
      <BenefitIconSet
        name={CONTACT_ICONS[contactType]}
        size={20}
        color={
          isSelected
            ? color.CONTACTS_SELECTED_FILTER_ICON
            : color.CONTACTS_FILTER_ICON
        }
      />
    </TouchableOpacity>
  );
};

export default ContactFilterIcon;

const createStyles = ({ color }: Theme, isSelected: boolean) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: SPACING.XL,
      backgroundColor: isSelected
        ? color.CONTACTS_SELECTED_FILTER_BACKGROUND
        : color.CONTACTS_FILTER_BACKGROUND,
      borderRadius: 6,
    },
  });

  return { styles, color };
};
