import React from "react";
import { StyleSheet, View, LayoutChangeEvent, ViewStyle } from "react-native";
import { type Theme, useThemeAwareObject } from "b-ui-lib";
import ContactList from "./ContactList";
import { ContactView } from "../../types/contactView";

type Props = {
  contactsData: ContactView[];
  listTitle: string;
  emptyListMessage: string;
  isLoading: boolean;
  handleRefreshList: () => void;
  handleTapContact: (contactId: string) => void;
  // Props passed by SearchBarAnimationHOC via cloneElement
  onScroll?: Function;
  listStyle?: ViewStyle | ViewStyle[];
  onLayout?: (event: LayoutChangeEvent) => void;
};

const ContactListScreen: React.FC<Props> = ({
  contactsData,
  listTitle,
  emptyListMessage,
  isLoading,
  handleRefreshList,
  handleTapContact,
  onScroll,
  listStyle,
  onLayout,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <ContactList
        data={contactsData}
        title={listTitle}
        emptyListMessage={emptyListMessage}
        isLoading={isLoading}
        handleRefreshList={handleRefreshList}
        handleTapContact={handleTapContact}
        onScroll={onScroll}
        listStyle={listStyle}
        onLayout={onLayout}
      />
    </View>
  );
};

export default ContactListScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
