import { createSlice } from "@reduxjs/toolkit";
import { GridMessageDto } from "../types/DTOs/gridMessageDto";
import { Folder, Folders } from "../types/folder";
import { FolderDTO } from "../types/DTOs/FolderDTO";
import { MetadataListDTO } from "../types/DTOs/MetadataDTO";
import { CommentListDTO } from "../types/DTOs/CommentDTO";
import {
  mapCaseMessage,
  mapMessage,
  mapMessageActions,
  mapMessageFolders,
} from "../helpers/mapMessage";
import { CaseMessageDTO } from "../types/DTOs/caseMessageDTO";
import { Message } from "../types/message";
import { mapFolder, mapSearchResultsFolder } from "../helpers/mapFolder";
import { FolderCountDTO } from "../types/DTOs/FolderCountDTO";
import { FoldersUserSettingsDTO } from "../types/DTOs/FoldersUserSettingsDTO";
import { FOLDER_NAMES } from "../constants/folderNames";
import { SEARCH_RESULTS_FOLDER_ID } from "../constants/searchResultsFolder";

// Initial state with Messages and Folders structures
const initialState = {
  gridMessageCount: 0,
  offset: 0,
  gridMessages: {
    byId: {} as Record<string, Message>,
    allIds: [] as string[],
  },
  gridMessagesLoading: false,
  gridMessagesLoadingMore: false,
  gridMessagesError: null,
  getMessageBodyLoading: {} as Record<string, boolean>,
  getMessageCommentsLoading: false,
  getMessageMetadataLoading: false,
  getMessageActionsLoading: false,
  getMessageBodyError: "",
  getMessageCommentsError: "",
  getMessageMetadataError: "",
  getMessageActionsError: "",
  copyOrMoveMessageFolderError: "",
  copyOrMoveMessageFolderSuccess: "",
  uploadAttachmentLoading: false,
  uploadAttachmentError: "",
  folders: {
    byId: {},
    allIds: [],
    rootIds: [], //Ids of the root folders
  } as Folders,
  selectedFolderId: "", // Currently selected folder ID
  isLoading: false, // Indicates if folder data is loading
  error: null as string | null, // Error message, if any
  folderChildrenErrorMessage: "",
  foldersCountIsLoading: false,
  foldersCountErrorMessage: "",
  messageFolders: {
    byId: {} as Record<string, any>,
    allIds: [] as string[],
  },
  // Related messages state
  relatedMessages: {
    conversation: {
      byId: {} as Record<string, Message>,
      allIds: [] as string[],
    },
    fromSender: {
      byId: {} as Record<string, Message>,
      allIds: [] as string[],
    },
  },
  relatedMessagesCount: {
    conversation: 0,
    fromSender: 0,
  },
  relatedMessagesLoading: false,
  relatedMessagesLoadingMore: false,
  relatedMessagesError: null,
};

const gridMessageSlice = createSlice({
  name: "gridMessages",
  initialState,
  reducers: {
    getFolders(state) {
      state.isLoading = true;
      state.error = "";
    },
    getFoldersSuccess(state, action) {
      const { responseBody } = action.payload || {};
      const foldersData: FolderDTO[] = responseBody;
      const newFoldersById: { [key: string]: Folder } = {};
      const newAllIds: string[] = [];

      foldersData.forEach((folder) => {
        newFoldersById[folder.FLD_Guid] = mapFolder(
          folder,
          state.folders.byId?.[folder.FLD_Guid]
        );
        newAllIds.push(folder.FLD_Guid);
      });

      state.folders.byId = {
        ...state.folders.byId,
        ...newFoldersById,
      };
      state.folders.allIds = Array.from(
        new Set([...state.folders.allIds, ...newAllIds])
      );
      state.folders.rootIds = newAllIds;
      state.selectedFolderId =
        state.selectedFolderId &&
        state.selectedFolderId !== SEARCH_RESULTS_FOLDER_ID
          ? state.selectedFolderId
          : newAllIds.find(
              (folderId) => newFoldersById[folderId].name === "All"
            ) || "";
      state.isLoading = false;
    },
    getFoldersFailed(state, action) {
      state.isLoading = false;
      state.error = "Error while retrieving folders";
    },
    getFolderChildren(state, action) {
      const { folderId } = action.payload;

      if (folderId && state.folders.byId?.[folderId]) {
        state.folders.byId[folderId] = {
          ...state.folders.byId[folderId],
          isLoading: true,
        };
      }

      state.folderChildrenErrorMessage = "";
    },
    getFolderChildrenSuccess(state, action) {
      const { responseBody, folderId } = action.payload || {};
      const foldersData: FolderDTO[] = responseBody;

      const newFoldersById: { [key: string]: Folder } = {};
      const newAllIds: string[] = [];

      foldersData.forEach((folder) => {
        newFoldersById[folder.FLD_Guid] = mapFolder(
          folder,
          state.folders.byId?.[folder.FLD_Guid]
        );
        newAllIds.push(folder.FLD_Guid);
      });

      state.folders.byId = {
        ...state.folders.byId,
        ...newFoldersById,
        [folderId]: {
          ...state.folders.byId?.[folderId],
          isLoading: false,
          childrenIds: newAllIds,
        },
      };
      state.folders.allIds = Array.from(
        new Set([...state.folders.allIds, ...newAllIds])
      );
      state.folderChildrenErrorMessage = "";
    },
    getFolderChildrenFailed(state, action) {
      const { folderId } = action.payload;

      if (folderId && state.folders.byId?.[folderId]) {
        state.folders.byId[folderId] = {
          ...state.folders.byId[folderId],
          isLoading: false,
        };
      }

      state.folderChildrenErrorMessage =
        "Something went wrong while retrieving folder children";
    },
    getFoldersCount(state) {
      state.foldersCountIsLoading = true;
      state.foldersCountErrorMessage = "";
    },
    getFoldersCountSuccess(state, action) {
      const { responseBody } = action.payload || {};
      const foldersData: FolderCountDTO[] = responseBody;

      const newFoldersById = foldersData.reduce((acc, folder) => {
        const existingFolder = state.folders.byId[folder.FLD_Guid];
        if (existingFolder) {
          acc[folder.FLD_Guid] = {
            ...existingFolder,
            emailsCount: folder.FLD_Count,
          };
        }
        return acc;
      }, {} as Record<string, Folder>);

      state.folders.byId = {
        ...state.folders.byId,
        ...newFoldersById,
      };

      state.foldersCountIsLoading = false;
    },
    getFoldersCountFailed(state, action) {
      state.foldersCountIsLoading = false;
      state.foldersCountErrorMessage =
        "Something went wrong while retrieving folder count";
    },
    getFoldersUserSettings(state) {
      state.foldersCountIsLoading = true;
      state.foldersCountErrorMessage = "";
    },
    getFoldersUserSettingsSuccess(state, action) {
      const { responseBody } = action.payload || {};
      const foldersData: FoldersUserSettingsDTO[] = responseBody;

      const newFoldersById = foldersData.reduce((acc, folder) => {
        const existingFolder = state.folders.byId[folder.UFS_FLD_Guid];
        if (existingFolder) {
          acc[folder.UFS_FLD_Guid] = {
            ...existingFolder,
            emailsCount: folder.UFS_UnreadMessageCounter,
          };
        }
        return acc;
      }, {} as Record<string, Folder>);

      state.folders.byId = {
        ...state.folders.byId,
        ...newFoldersById,
      };

      state.foldersCountIsLoading = false;
    },
    getFoldersUserSettingsFailed(state, action) {
      state.foldersCountIsLoading = false;
      state.foldersCountErrorMessage =
        "Something went wrong while retrieving folder user settings";
    },
    setSelectedFolder(state, action) {
      state.selectedFolderId = action.payload;
    },
    toggleFolderExpand(state, action) {
      state.folders.byId[action.payload].isExpanded =
        !state.folders.byId[action.payload].isExpanded;
    },
    archiveToFolder(state, action) {
      return state;
    },
    setFromSettings(state, action) {
      return state;
    },
    setFromArchiveMessage(state, action) {
      return state;
    },
    updateFolderMessages: (state, { payload }) => {},
    fetchGridMessages(state, { payload }) {
      if (payload?.offset) {
        state.gridMessagesLoadingMore = true;
      } else {
        state.gridMessagesLoading = true;
      }
    },
    fetchGridMessageSuccess(state, action) {
      const { folderGuid, responseBody, offset, isSearching } = action.payload;

      const newIds: string[] = responseBody.value.map(
        (message) => message.UMS_Guid
      );

      // Update gridMessages.byId without overwriting existing data
      responseBody.value.forEach((message: GridMessageDto) => {
        const { UMS_Guid } = message;
        state.gridMessages.byId[UMS_Guid] = mapMessage(
          message,
          state.gridMessages.byId[UMS_Guid]
        );
      });

      // Handle message ordering correctly for pagination
      if (offset) {
        // For pagination (load more), append new messages to the end
        state.gridMessages.allIds = Array.from(
          new Set([...state.gridMessages.allIds, ...newIds])
        );
      } else {
        // For initial load or refresh, replace with new messages
        state.gridMessages.allIds = Array.from(new Set([...newIds]));
      }

      // Ensure gridMessageCount is updated correctly from response
      state.gridMessageCount = parseInt(responseBody["odata.count"], 10) || 0;
      state.gridMessagesLoading = false;
      state.gridMessagesLoadingMore = false;
      state.gridMessagesError = null;
      state.offset = offset ? state.offset + offset : 0;

      // Update the folder's emailIds (merge instead of replacing)
      const folder = state.folders.byId[folderGuid];

      if (state.folders.byId[folderGuid] && !isSearching) {
        offset
          ? // For pagination, append new messages to the end
            (state.folders.byId[folderGuid].emailIds = Array.from(
              new Set([...folder.emailIds, ...newIds])
            ))
          : // For initial load or refresh, replace with new messages
            (state.folders.byId[folderGuid].emailIds = Array.from(
              new Set([...newIds])
            ));
      }
    },
    fetchGridMessageFailed(state, { payload }) {
      state.gridMessagesError =
        payload[0]?.response?.body["odata.error"]?.message?.value ||
        "Error fetching inbox items";
      state.gridMessagesLoading = false;
      state.gridMessagesLoadingMore = false;
    },
    clearOffset(state) {
      state.offset = 0;
    },
    clearAllErrors(state) {
      state.gridMessagesError = null;
      state.getMessageBodyError = "";
      state.getMessageCommentsError = "";
      state.getMessageActionsError = "";
      state.getMessageMetadataError = "";
      state.copyOrMoveMessageFolderError = "";
    },
    performDeleteAction(state, action) {
      // Placeholder for deleting selected emails
    },
    performDeleteActionSuccess(state, { payload }) {
      const { sourceFolder, destinationFolder, selectedMessageIds } = payload;
      const deletedItemsFolderId = state.folders.allIds.find(
        (id) => state.folders.byId[id].name === FOLDER_NAMES.deletedItems
      );

      if (!deletedItemsFolderId) return;

      selectedMessageIds.forEach((id: string) => {
        // Update the sourceFolderDeletedFrom property
        state.gridMessages.byId[id].sourceFolderDeletedFrom =
          state.selectedFolderId;
        // Remove the email ID from the source folder
        state.folders.byId[sourceFolder].emailIds = state.folders.byId[
          sourceFolder
        ].emailIds.filter((folderEmailId) => folderEmailId !== id);

        // Add the email ID to the deleted items folder
        state.folders.byId[deletedItemsFolderId].emailIds.push(id);
      });
    },
    performDeleteActionFailed(state) {
      // Placeholder for deleting selected emails
    },
    performUnDeleteAction(state, action) {
      // Placeholder for deleting selected emails
    },
    performUnDeleteActionSuccess(state, { payload }) {
      const { sourceFolder, destinationFolder, selectedMessageIds } = payload;
      const deletedItemsFolderId = state.folders.allIds.find(
        (id) => state.folders.byId[id].name === FOLDER_NAMES.deletedItems
      );

      if (!deletedItemsFolderId) return;

      selectedMessageIds.forEach((id: string) => {
        // Update the sourceFolderDeletedFrom property
        state.gridMessages.byId[id].sourceFolderDeletedFrom = "";
        // Remove the email from the deleted items folder
        state.folders.byId[deletedItemsFolderId].emailIds = state.folders.byId[
          deletedItemsFolderId
        ].emailIds.filter((folderEmailId: string) => folderEmailId !== id);
      });
    },
    performMarkAsReadUnreadAction(state, { payload }) {
      // Optimistic update: immediately change the read/unread state
      const { ids, mode } = payload || {};

      if (ids && Array.isArray(ids)) {
        ids.forEach((guid: string) => {
          if (state.gridMessages.byId?.[guid]) {
            state.gridMessages.byId[guid] = {
              ...state.gridMessages.byId[guid],
              isViewed: mode !== 7, // Mode 6 = read (true), Mode 7 = unread (false)
            };
          }
        });
      }
    },
    performMarkAsReadUnreadActionSuccess(state, action) {
      const { UMS_Guids, Mode } = action.payload?.responseBody || {};

      // Confirm the optimistic update with server response
      UMS_Guids.forEach((guid: string) => {
        if (state.gridMessages.byId?.[guid]) {
          state.gridMessages.byId[guid] = {
            ...state.gridMessages.byId?.[guid],
            isViewed: Mode !== 7,
          };
        }
      });
    },
    performMarkAsReadUnreadActionFailed(state, action) {
      // This will be handled by performMarkAsReadUnreadActionRevert action
    },
    performMarkAsReadUnreadActionRevert(state, action) {
      // Revert the optimistic update on failure
      const { ids, originalMode } = action.payload || {};

      if (ids && Array.isArray(ids)) {
        ids.forEach((guid: string) => {
          if (state.gridMessages.byId?.[guid]) {
            state.gridMessages.byId[guid] = {
              ...state.gridMessages.byId[guid],
              isViewed: originalMode === 7, // Revert to opposite of what was attempted
            };
          }
        });
      }
    },
    moveEmailsToFolder(state, action) {
      state.copyOrMoveMessageFolderError = "";
      state.copyOrMoveMessageFolderSuccess = "";
    },
    moveEmailsToFolderSuccess(state, { payload }) {
      // Move selected emails to a specific folder
      const { sourceFolder, destinationFolder, selectedMessageIds } = payload;

      selectedMessageIds.forEach((id) => {
        // Remove the email ID from the source folder
        state.folders.byId[sourceFolder].emailIds = state.folders.byId[
          sourceFolder
        ].emailIds.filter((folderEmailId) => folderEmailId !== id);

        // Add the email ID to the deleted items folder
        state.folders.byId[destinationFolder].emailIds.push(id);
      });
      state.copyOrMoveMessageFolderError = "";
      state.copyOrMoveMessageFolderSuccess =
        "Email moved successfully to folder.";
    },
    moveEmailsToFolderFailed(state, { payload }) {
      state.copyOrMoveMessageFolderError = payload[0]?.response?.body?.Message;
      state.copyOrMoveMessageFolderSuccess = "";
    },
    copyEmailsToFolder(state, action) {
      state.copyOrMoveMessageFolderError = "";
      state.copyOrMoveMessageFolderSuccess = "";
    },
    copyEmailsToFolderSuccess(state, { payload }) {
      // copy selected emails to a specific folder
      const { sourceFolder, destinationFolder, selectedMessageIds } = payload;

      selectedMessageIds.forEach((id) => {
        // Add the email ID to the deleted items folder
        state.folders.byId[destinationFolder].emailIds.push(id);
      });
      state.copyOrMoveMessageFolderError = "";
      state.copyOrMoveMessageFolderSuccess =
        "Email copied successfully to folder";
    },
    copyEmailsToFolderFailed(state, { payload }) {
      state.copyOrMoveMessageFolderError = payload[0]?.response?.body?.Message;
      state.copyOrMoveMessageFolderSuccess = "";
    },
    clearCopyOrMoveMessageFolderError(state) {
      state.copyOrMoveMessageFolderError = "";
    },
    clearCopyOrMoveMessageFolderSuccess(state) {
      state.copyOrMoveMessageFolderSuccess = "";
    },
    linkEmailsToCase(state, action) {
      // Link selected emails to a case based on action.payload
    },
    loadMoreEmailsOnScroll(state) {
      // Placeholder for loading more emails when scrolling down
    },
    getMessageBody: (state, action) => {
      const { UMS_Guid } = action.payload || {};
      if (UMS_Guid) {
        state.getMessageBodyLoading[UMS_Guid] = true;
      }
      state.getMessageBodyError = "";
    },
    getMessageBodySuccess: (state, { payload }) => {
      const { UMS_Guid } = payload || {};
      if (UMS_Guid) {
        state.gridMessages.byId[UMS_Guid].fullMessageBody = payload.messageBody;
        state.getMessageBodyLoading[UMS_Guid] = false;
      }
    },
    getMessageBodyFailed: (state, { payload }) => {
      // Extract UMS_Guid from the failed request if available
      const UMS_Guid = payload[0]?.request?.send?.UMS_Guid;
      state.getMessageBodyError = payload[0].response?.Message;
      if (UMS_Guid) {
        state.getMessageBodyLoading[UMS_Guid] = false;
      }
    },
    getMessageBodyNext: (state, action) => {},
    getMessageBodyPrevious: (state, action) => {},
    flagMessages: (state, action) => {
      // Optimistic update: immediately change the flag state
      const { ids, value } = action.payload || {};

      if (ids && Array.isArray(ids)) {
        ids.forEach((guid: string) => {
          if (state.gridMessages.byId?.[guid]) {
            state.gridMessages.byId[guid] = {
              ...state.gridMessages.byId[guid],
              isFlagged: value !== 0,
            };
          }
        });
      }
    },
    flagMessagesSuccess: (state, action) => {
      const { UMS_Guids, Value } = action.payload?.responseBody || {};

      // Confirm the optimistic update with server response
      UMS_Guids.forEach((guid: string) => {
        if (state.gridMessages.byId?.[guid]) {
          state.gridMessages.byId[guid] = {
            ...state.gridMessages.byId?.[guid],
            isFlagged: Value !== 0,
          };
        }
      });
    },
    flagMessagesFailed: (state, action) => {
      // This will be handled by flagMessagesRevert action
    },
    flagMessagesRevert: (state, action) => {
      // Revert the optimistic update on failure
      const { ids, originalValue } = action.payload || {};

      if (ids && Array.isArray(ids)) {
        ids.forEach((guid: string) => {
          if (state.gridMessages.byId?.[guid]) {
            state.gridMessages.byId[guid] = {
              ...state.gridMessages.byId[guid],
              isFlagged: originalValue !== 0,
            };
          }
        });
      }
    },
    getMessageComments: (state, action) => {
      state.getMessageCommentsLoading = true;
    },
    getMessageCommentsSuccess: (state, { payload }) => {
      const response: CommentListDTO = JSON.parse(payload.messageBody);
      const message = state.gridMessages.byId[payload.UMS_Guid];

      state.getMessageCommentsError = "";
      if (message) {
        message.messageCommentIds = response?.map(
          (comment) => comment.CMM_Guid
        );
      }
      state.getMessageCommentsLoading = false;
    },
    getMessageCommentsFailed: (state, { payload }) => {
      state.getMessageCommentsLoading = false;
      state.getMessageCommentsError = payload[0].response?.Message;
    },
    getMessageMetadata: (state, action) => {
      state.getMessageMetadataLoading = true;
    },
    getMessageMetadataSuccess: (state, { payload }) => {
      const response = JSON.parse(payload.messageBody);

      const mapMetadata = (metadata: MetadataListDTO) => {
        return metadata.flatMap((item) =>
          item.lstMFEV.map((mfev) => ({
            id: item.objMFE.MFE_Guid, // Use MFE_Guid for the id
            name: item.objMFL.MFL_Abbreviation, // Use MFL_Abbreviation for the name
            description: mfev.MEV_Value, // Use MEV_Value for the description
          }))
        );
      };

      state.getMessageMetadataError = "";
      state.gridMessages.byId[payload.UMS_Guid].messageMetadata =
        mapMetadata(response);
      state.getMessageMetadataLoading = false;
    },
    getMessageMetadataFailed: (state, { payload }) => {
      state.getMessageCommentsLoading = false;
      state.getMessageMetadataError = payload[0].response?.Message;
    },
    getMessageActions: (state, action) => {
      state.getMessageActionsLoading = true;
      state.getMessageActionsError = "";
    },
    getMessageActionsPrevious: (state, action) => {},
    getMessageActionsNext: (state, action) => {},
    getMessageActionsSuccess: (state, { payload }) => {
      const response = JSON.parse(payload);
      const messageId = response?.UMS_Guid;

      state.getMessageActionsError = "";
      state.getMessageActionsLoading = false;
      state.gridMessages.byId[messageId] = mapMessageActions(
        response,
        state.gridMessages.byId[messageId]
      );
      state.messageFolders = mapMessageFolders(response, state.messageFolders);
    },
    getMessageActionsFailed: (state, { payload }) => {
      state.getMessageActionsLoading = false;
      state.getMessageActionsError = payload[0]?.response?.body?.Message;
    },
    storeCaseMessages: (state, { payload }) => {
      const { responseBody, caseId } = (payload || {}) as {
        responseBody?: CaseMessageDTO[];
        caseId: string;
      };

      const newMessages = (responseBody || []).reduce((acc, message) => {
        const messageId = message?.UMS_Guid;
        acc[messageId] = mapCaseMessage(
          message,
          state.gridMessages.byId?.[messageId]
        );
        return acc;
      }, {} as Record<string, Message>);

      state.gridMessages.byId = {
        ...state.gridMessages.byId,
        ...(newMessages || {}),
      };
    },
    // Related messages actions
    fetchRelatedMessages: (state, { payload }) => {
      const { skipFirst } = payload || {};

      if (skipFirst) {
        state.relatedMessagesLoadingMore = true;
      } else {
        state.relatedMessagesLoading = true;
      }

      state.relatedMessagesError = null;

      // Ensure the relatedMessages collections are properly initialized
      if (!state.relatedMessages) {
        state.relatedMessages = {
          conversation: {
            byId: {} as Record<string, Message>,
            allIds: [] as string[],
          },
          fromSender: {
            byId: {} as Record<string, Message>,
            allIds: [] as string[],
          },
        };
      }

      if (!state.relatedMessages.conversation) {
        state.relatedMessages.conversation = {
          byId: {} as Record<string, Message>,
          allIds: [] as string[],
        };
      }

      if (!state.relatedMessages.fromSender) {
        state.relatedMessages.fromSender = {
          byId: {} as Record<string, Message>,
          allIds: [] as string[],
        };
      }
    },
    fetchRelatedMessagesSuccess: (state, { payload }) => {
      const { responseBody, type, skipFirst } = payload;

      // Ensure the relatedMessages collections are properly initialized
      if (!state.relatedMessages) {
        state.relatedMessages = {
          conversation: { byId: {}, allIds: [] },
          fromSender: { byId: {}, allIds: [] },
        };
      }

      if (!state.relatedMessages.conversation) {
        state.relatedMessages.conversation = { byId: {}, allIds: [] };
      }

      if (!state.relatedMessages.fromSender) {
        state.relatedMessages.fromSender = { byId: {}, allIds: [] };
      }

      // Determine which related messages collection to update based on type
      const targetCollection = type === 1 ? "conversation" : "fromSender";

      // Handle case where responseBody or responseBody.value might be undefined
      const messages = responseBody?.value || [];
      const newIds: string[] = messages
        .map((message: any) => message.UMS_Guid)
        .filter(Boolean);

      // Update gridMessages.byId without overwriting existing data
      messages.forEach((message: any) => {
        if (message && message.UMS_Guid) {
          const { UMS_Guid } = message;
          state.gridMessages.byId[UMS_Guid] = mapMessage(
            message,
            state.gridMessages.byId[UMS_Guid]
          );
        }
      });

      // Update the related messages collection
      if (state.relatedMessages[targetCollection]) {
        if (skipFirst && skipFirst > 0) {
          // If loading more, append the new IDs without duplicates
          const existingIds =
            state.relatedMessages[targetCollection].allIds || [];
          const combinedIds = [...existingIds];

          // Only add IDs that don't already exist
          newIds.forEach((id) => {
            if (!existingIds.includes(id)) {
              combinedIds.push(id);
            }
          });

          state.relatedMessages[targetCollection].allIds = combinedIds;
        } else {
          // If initial load, replace the IDs
          state.relatedMessages[targetCollection].allIds = [...newIds];
        }
      }

      // Update the count
      if (responseBody && responseBody["odata.count"]) {
        // Ensure relatedMessagesCount is initialized
        if (!state.relatedMessagesCount) {
          state.relatedMessagesCount = {
            conversation: 0,
            fromSender: 0,
          };
        }
        state.relatedMessagesCount[targetCollection] =
          parseInt(responseBody["odata.count"], 10) || 0;
      }

      state.relatedMessagesLoading = false;
      state.relatedMessagesLoadingMore = false;
    },
    fetchRelatedMessagesFailed: (state, { payload }) => {
      // Ensure the relatedMessages collections are properly initialized
      if (!state.relatedMessages) {
        state.relatedMessages = {
          conversation: { byId: {}, allIds: [] },
          fromSender: { byId: {}, allIds: [] },
        };
      }

      state.relatedMessagesError =
        payload[0]?.response?.body?.["odata.error"]?.message?.value ||
        "Error fetching related messages";
      state.relatedMessagesLoading = false;
      state.relatedMessagesLoadingMore = false;
    },
    // Search Results Folder actions
    createSearchResultsFolder(state, action) {
      const { searchResultsFolderIds } = action.payload || {};

      state.folders.byId[SEARCH_RESULTS_FOLDER_ID] = mapSearchResultsFolder(
        searchResultsFolderIds
      );

      state.folders.allIds = Array.from(
        new Set([...state.folders.allIds, SEARCH_RESULTS_FOLDER_ID])
      );

      state.folders.rootIds = Array.from(
        new Set([...state.folders.rootIds, SEARCH_RESULTS_FOLDER_ID])
      );
    },
    deleteSearchResultsFolder(state) {
      if (state.folders.byId[SEARCH_RESULTS_FOLDER_ID]) {
        delete state.folders.byId[SEARCH_RESULTS_FOLDER_ID];

        state.folders.allIds = state.folders.allIds.filter(
          (id) => id !== SEARCH_RESULTS_FOLDER_ID
        );

        state.folders.rootIds = state.folders.rootIds.filter(
          (id) => id !== SEARCH_RESULTS_FOLDER_ID
        );
      }
    },
  },
});

// Export the action creators for dispatching
export const {
  getFolders,
  getFoldersSuccess,
  getFoldersFailed,
  getFolderChildren,
  getFolderChildrenSuccess,
  getFolderChildrenFailed,
  getFoldersCount,
  getFoldersCountSuccess,
  getFoldersCountFailed,
  getFoldersUserSettings,
  getFoldersUserSettingsSuccess,
  getFoldersUserSettingsFailed,
  toggleFolderExpand,
  setSelectedFolder,
  archiveToFolder,
  setFromSettings,
  setFromArchiveMessage,
  updateFolderMessages,
  performDeleteAction,
  performDeleteActionSuccess,
  performDeleteActionFailed,
  performUnDeleteAction,
  performUnDeleteActionSuccess,
  performMarkAsReadUnreadAction,
  performMarkAsReadUnreadActionSuccess,
  performMarkAsReadUnreadActionFailed,
  performMarkAsReadUnreadActionRevert,
  moveEmailsToFolder,
  moveEmailsToFolderSuccess,
  moveEmailsToFolderFailed,
  copyEmailsToFolder,
  copyEmailsToFolderSuccess,
  copyEmailsToFolderFailed,
  clearCopyOrMoveMessageFolderError,
  clearCopyOrMoveMessageFolderSuccess,
  linkEmailsToCase,
  loadMoreEmailsOnScroll,
  fetchGridMessages,
  fetchGridMessageSuccess,
  fetchGridMessageFailed,
  clearOffset,
  clearAllErrors,
  getMessageBody,
  getMessageBodyFailed,
  getMessageBodySuccess,
  getMessageBodyNext,
  getMessageBodyPrevious,
  flagMessages,
  flagMessagesSuccess,
  flagMessagesFailed,
  flagMessagesRevert,
  getMessageComments,
  getMessageCommentsSuccess,
  getMessageCommentsFailed,
  getMessageMetadata,
  getMessageMetadataSuccess,
  getMessageMetadataFailed,
  getMessageActions,
  getMessageActionsPrevious,
  getMessageActionsNext,
  getMessageActionsSuccess,
  getMessageActionsFailed,
  storeCaseMessages,
  // Related messages actions
  fetchRelatedMessages,
  fetchRelatedMessagesSuccess,
  fetchRelatedMessagesFailed,
  createSearchResultsFolder,
  deleteSearchResultsFolder,
} = gridMessageSlice.actions;

export default gridMessageSlice.reducer;
