export type Folders = {
  byId: {
    [key: string]: Folder;
  };
  allIds: string[];
  rootIds: string[];
};

export type Folder = {
  id: string;
  parentFolderId?: string | null;
  folderCriteriaId?: string | null;
  name: string;
  level?: number | null;
  sortOrder?: number | null;
  isVisible?: boolean | null;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
  deletedBy?: string | null;
  status: number;
  isExpanded: boolean;
  isSearchItem: boolean;
  isLoading: boolean;
  emailsCount?: number | string;
  emailIds: string[];
  hasChildren: boolean;
  childrenIds: string[];
  searchResultsFolderIds: string[];
};
