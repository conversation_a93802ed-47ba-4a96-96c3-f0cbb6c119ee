PODS:
  - boost (1.83.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.11)
  - FBReactNativeSpec (0.73.11):
    - RCT-<PERSON>olly (= 2022.05.16.00)
    - RCTRequired (= 0.73.11)
    - RCTTypeSafety (= 0.73.11)
    - React-Core (= 0.73.11)
    - React-jsi (= 0.73.11)
    - ReactCommon/turbomodule/core (= 0.73.11)
  - Firebase/Analytics (11.5.0):
    - Firebase/Core
  - Firebase/Core (11.5.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.5.0)
  - Firebase/CoreOnly (11.5.0):
    - FirebaseCore (= 11.5.0)
  - Firebase/Crashlytics (11.5.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.5.0)
  - FirebaseAnalytics (11.5.0):
    - FirebaseAnalytics/AdIdSupport (= 11.5.0)
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.5.0):
    - FirebaseCoreInternal (= 11.5)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.5.0):
    - FirebaseCore (= 11.5)
  - FirebaseCoreInternal (11.5.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.5.0):
    - FirebaseCore (= 11.5)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSessions (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseCoreExtension (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.1.0):
    - Google-Maps-iOS-Utils/Clustering (= 4.1.0)
    - Google-Maps-iOS-Utils/Geometry (= 4.1.0)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.1.0)
    - Google-Maps-iOS-Utils/Heatmap (= 4.1.0)
    - Google-Maps-iOS-Utils/QuadTree (= 4.1.0)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (4.1.0):
    - GoogleMaps
  - GoogleAppMeasurement (11.5.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.5.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.5.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.73.11):
    - hermes-engine/Pre-built (= 0.73.11)
  - hermes-engine/Pre-built (0.73.11)
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NextLevelSessionExporter (0.4.6)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.11)
  - RCTTypeSafety (0.73.11):
    - FBLazyVector (= 0.73.11)
    - RCTRequired (= 0.73.11)
    - React-Core (= 0.73.11)
  - React (0.73.11):
    - React-Core (= 0.73.11)
    - React-Core/DevSupport (= 0.73.11)
    - React-Core/RCTWebSocket (= 0.73.11)
    - React-RCTActionSheet (= 0.73.11)
    - React-RCTAnimation (= 0.73.11)
    - React-RCTBlob (= 0.73.11)
    - React-RCTImage (= 0.73.11)
    - React-RCTLinking (= 0.73.11)
    - React-RCTNetwork (= 0.73.11)
    - React-RCTSettings (= 0.73.11)
    - React-RCTText (= 0.73.11)
    - React-RCTVibration (= 0.73.11)
  - React-callinvoker (0.73.11)
  - React-Codegen (0.73.11):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-Core/RCTWebSocket (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.11)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.11)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.11)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.11):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-debug (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-jsinspector (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
    - React-runtimeexecutor (= 0.73.11)
  - React-debug (0.73.11)
  - React-Fabric (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.11)
    - React-Fabric/attributedstring (= 0.73.11)
    - React-Fabric/componentregistry (= 0.73.11)
    - React-Fabric/componentregistrynative (= 0.73.11)
    - React-Fabric/components (= 0.73.11)
    - React-Fabric/core (= 0.73.11)
    - React-Fabric/imagemanager (= 0.73.11)
    - React-Fabric/leakchecker (= 0.73.11)
    - React-Fabric/mounting (= 0.73.11)
    - React-Fabric/scheduler (= 0.73.11)
    - React-Fabric/telemetry (= 0.73.11)
    - React-Fabric/templateprocessor (= 0.73.11)
    - React-Fabric/textlayoutmanager (= 0.73.11)
    - React-Fabric/uimanager (= 0.73.11)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.11)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.11)
    - React-Fabric/components/modal (= 0.73.11)
    - React-Fabric/components/rncore (= 0.73.11)
    - React-Fabric/components/root (= 0.73.11)
    - React-Fabric/components/safeareaview (= 0.73.11)
    - React-Fabric/components/scrollview (= 0.73.11)
    - React-Fabric/components/text (= 0.73.11)
    - React-Fabric/components/textinput (= 0.73.11)
    - React-Fabric/components/unimplementedview (= 0.73.11)
    - React-Fabric/components/view (= 0.73.11)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.11)
    - RCTTypeSafety (= 0.73.11)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.11)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.11):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-utils
  - React-hermes (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.11)
    - React-jsi
    - React-jsiexecutor (= 0.73.11)
    - React-jsinspector (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - React-ImageManager (0.73.11):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.11):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.11):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - React-jsinspector (0.73.11)
  - React-logger (0.73.11):
    - glog
  - React-Mapbuffer (0.73.11):
    - glog
    - React-debug
  - react-native-background-actions (4.0.1):
    - React-Core
  - react-native-blob-util (0.19.11):
    - React-Core
  - react-native-compressor (1.8.15):
    - glog
    - NextLevelSessionExporter
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-config (1.5.3):
    - react-native-config/App (= 1.5.3)
  - react-native-config/App (1.5.3):
    - React-Core
  - react-native-document-picker (8.2.0):
    - React-Core
  - react-native-google-maps (1.7.1):
    - Google-Maps-iOS-Utils (= 4.1.0)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-image-picker (5.0.2):
    - React-Core
  - react-native-image-resizer (3.0.11):
    - React-Core
  - react-native-maps (1.7.1):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-pdf (6.7.7):
    - React-Core
  - react-native-photo-editor (1.1.6):
    - React-Core
    - react-native-photo-editor/ZLImageEditor (= 1.1.6)
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - react-native-photo-editor/ZLImageEditor (1.1.6):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (3.4.1):
    - React-Core
  - react-native-slider (4.4.2):
    - React-Core
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-nativeconfig (0.73.11)
  - React-NativeModulesApple (0.73.11):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.11)
  - React-RCTActionSheet (0.73.11):
    - React-Core/RCTActionSheetHeaders (= 0.73.11)
  - React-RCTAnimation (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.11):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.11):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.11):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.11)
  - React-RCTNetwork (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.11):
    - React-Core/RCTTextHeaders (= 0.73.11)
    - Yoga
  - React-RCTVibration (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.11)
  - React-runtimeexecutor (0.73.11):
    - React-jsi (= 0.73.11)
  - React-runtimescheduler (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.11):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.11):
    - React-logger (= 0.73.11)
    - ReactCommon/turbomodule (= 0.73.11)
  - ReactCommon/turbomodule (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
    - ReactCommon/turbomodule/bridging (= 0.73.11)
    - ReactCommon/turbomodule/core (= 0.73.11)
  - ReactCommon/turbomodule/bridging (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - ReactCommon/turbomodule/core (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - RNAudioRecorderPlayer (3.6.12):
    - React-Core
  - RNCAsyncStorage (2.1.0):
    - React-Core
  - RNCClipboard (1.14.2):
    - React-Core
  - RNCPicker (2.4.10):
    - React-Core
  - RNDateTimePicker (8.2.0):
    - React-Core
  - RNDeviceInfo (10.6.0):
    - React-Core
  - RNFBAnalytics (21.5.0):
    - Firebase/Analytics (= 11.5.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.5.0):
    - Firebase/CoreOnly (= 11.5.0)
    - React-Core
  - RNFBCrashlytics (21.5.0):
    - Firebase/Crashlytics (= 11.5.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.20.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNNotifee (7.8.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.8.0)
  - RNNotifee/NotifeeCore (7.8.0):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.2)
    - RNReanimated/worklets (= 3.16.2)
  - RNReanimated/reanimated (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.2)
  - RNReanimated/reanimated/apple (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNReanimated/worklets (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.34.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTImage
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNSoundPlayer (0.13.4):
    - React-Core
  - RNSVG (15.9.0):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - RNZipArchive (7.0.1):
    - React-Core
    - RNZipArchive/Core (= 7.0.1)
    - SSZipArchive (~> 2.5.5)
  - RNZipArchive/Core (7.0.1):
    - React-Core
    - SSZipArchive (~> 2.5.5)
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.5.5)
  - VisionCamera (3.9.2):
    - React
    - React-callinvoker
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../../../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils (from `https://github.com/Simon-TechForm/google-maps-ios-utils.git`, branch `feat/support-apple-silicon`)
  - GoogleMaps (= 7.4.0)
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../../../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../../../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../../../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../../../node_modules/react-native/ReactCommon`)
  - react-native-background-actions (from `../../../node_modules/react-native-background-actions`)
  - react-native-blob-util (from `../../../node_modules/react-native-blob-util`)
  - react-native-compressor (from `../../../node_modules/react-native-compressor`)
  - react-native-config (from `../../../node_modules/react-native-config`)
  - react-native-document-picker (from `../../../node_modules/react-native-document-picker`)
  - react-native-google-maps (from `../../../node_modules/react-native-maps`)
  - react-native-image-picker (from `../../../node_modules/react-native-image-picker`)
  - "react-native-image-resizer (from `../../../node_modules/@bam.tech/react-native-image-resizer`)"
  - react-native-maps (from `../../../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../../../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../../../node_modules/react-native-pager-view`)
  - react-native-pdf (from `../../../node_modules/react-native-pdf`)
  - "react-native-photo-editor (from `../../../node_modules/@baronha/react-native-photo-editor`)"
  - react-native-render-html (from `../../../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../../../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../../../node_modules/@react-native-community/slider`)"
  - react-native-tracking-transparency (from `../../../node_modules/react-native-tracking-transparency`)
  - react-native-video (from `../../../node_modules/react-native-video`)
  - react-native-webview (from `../../../node_modules/react-native-webview`)
  - React-nativeconfig (from `../../../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../../../node_modules/react-native/React`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../../../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../../../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - RNAudioRecorderPlayer (from `../../../node_modules/react-native-audio-recorder-player`)
  - "RNCAsyncStorage (from `../../../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../../../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../../../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../../../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../../../node_modules/react-native-device-info`)
  - "RNFBAnalytics (from `../../../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../../../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../../../node_modules/@react-native-firebase/crashlytics`)"
  - RNFS (from `../../../node_modules/react-native-fs`)
  - RNGestureHandler (from `../../../node_modules/react-native-gesture-handler`)
  - "RNNotifee (from `../../../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../../../node_modules/react-native-permissions`)
  - RNReanimated (from `../../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../../node_modules/react-native-screens`)
  - RNSound (from `../../../node_modules/react-native-sound`)
  - RNSoundPlayer (from `../../../node_modules/react-native-sound-player`)
  - RNSVG (from `../../../node_modules/react-native-svg`)
  - RNVectorIcons (from `../../../node_modules/react-native-vector-icons`)
  - RNZipArchive (from `../../../node_modules/react-native-zip-archive`)
  - VisionCamera (from `../../../node_modules/react-native-vision-camera`)
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - libevent
    - libwebp
    - nanopb
    - NextLevelSessionExporter
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - SSZipArchive

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../../../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  Google-Maps-iOS-Utils:
    :branch: feat/support-apple-silicon
    :git: https://github.com/Simon-TechForm/google-maps-ios-utils.git
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-04-29-RNv0.73.8-644c8be78af1eae7c138fa4093fb87f0f4f8db85
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../../../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../../../node_modules/react-native/ReactCommon"
  react-native-background-actions:
    :path: "../../../node_modules/react-native-background-actions"
  react-native-blob-util:
    :path: "../../../node_modules/react-native-blob-util"
  react-native-compressor:
    :path: "../../../node_modules/react-native-compressor"
  react-native-config:
    :path: "../../../node_modules/react-native-config"
  react-native-document-picker:
    :path: "../../../node_modules/react-native-document-picker"
  react-native-google-maps:
    :path: "../../../node_modules/react-native-maps"
  react-native-image-picker:
    :path: "../../../node_modules/react-native-image-picker"
  react-native-image-resizer:
    :path: "../../../node_modules/@bam.tech/react-native-image-resizer"
  react-native-maps:
    :path: "../../../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../../../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../../../node_modules/react-native-pager-view"
  react-native-pdf:
    :path: "../../../node_modules/react-native-pdf"
  react-native-photo-editor:
    :path: "../../../node_modules/@baronha/react-native-photo-editor"
  react-native-render-html:
    :path: "../../../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../../../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../../../node_modules/@react-native-community/slider"
  react-native-tracking-transparency:
    :path: "../../../node_modules/react-native-tracking-transparency"
  react-native-video:
    :path: "../../../node_modules/react-native-video"
  react-native-webview:
    :path: "../../../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../../../node_modules/react-native/React"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../../../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  RNAudioRecorderPlayer:
    :path: "../../../node_modules/react-native-audio-recorder-player"
  RNCAsyncStorage:
    :path: "../../../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../../../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../../../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../../../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../../../node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../../../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../../../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../../../node_modules/@react-native-firebase/crashlytics"
  RNFS:
    :path: "../../../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../../../node_modules/react-native-gesture-handler"
  RNNotifee:
    :path: "../../../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../../../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../../node_modules/react-native-screens"
  RNSound:
    :path: "../../../node_modules/react-native-sound"
  RNSoundPlayer:
    :path: "../../../node_modules/react-native-sound-player"
  RNSVG:
    :path: "../../../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../../../node_modules/react-native-vector-icons"
  RNZipArchive:
    :path: "../../../node_modules/react-native-zip-archive"
  VisionCamera:
    :path: "../../../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

CHECKOUT OPTIONS:
  Google-Maps-iOS-Utils:
    :commit: 35d05d1eeb65682c1b271f4f3760d814fd946aa1
    :git: https://github.com/Simon-TechForm/google-maps-ios-utils.git

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: b46891061bfe0a9b07f601813114c8653a72a45c
  FBReactNativeSpec: 68b6a0ee435ac2626dbd6a3e736cc2d415a30f40
  Firebase: 7a56fe4f56b5ab81b86a6822f5b8f909ae6fc7e2
  FirebaseAnalytics: 2f4a11eeb7a0e9c6fcf642d4e6aaca7fa4d38c28
  FirebaseCore: 93abc05437f8064cd2bc0a53b768fb0bc5a1d006
  FirebaseCoreExtension: ddb2eb987f736b714d30f6386795b52c4670439e
  FirebaseCoreInternal: f47dd28ae7782e6a4738aad3106071a8fe0af604
  FirebaseCrashlytics: 94c11c3bf296fde8c18f2c9f8e76bd9349227038
  FirebaseInstallations: d8063d302a426d114ac531cd82b1e335a0565745
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSessions: b252b3f91a51186188882ea8e7e1730fc1eee391
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  Google-Maps-iOS-Utils: d3fdfd57db799771418f06189e33981597553aa8
  GoogleAppMeasurement: ee5c2d2242816773fbf79e5b0563f5355ef1c315
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  hermes-engine: d992945b77c506e5164e6a9a77510c9d57472c59
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NextLevelSessionExporter: 4d8aa5e617f1c709724f2453efe5d4628480f65a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: 415e56f7c33799a6483e41e4dce607f3daf1e69b
  RCTTypeSafety: e984a88e713281c2d8c2309a1a6d2775af0107ae
  React: ab885684e73c5f659bad63446a977312fd3d1ecb
  React-callinvoker: 50a2d1ce3594637c700401ba306373321231eb71
  React-Codegen: 35331de15e1216a8f81a3134a0b459416fddef66
  React-Core: 9d66a8a953d975aee4989abccf4602e7ba7c65fa
  React-CoreModules: e93a24aaae933d496329112cec78b681c76cdc53
  React-cxxreact: 8f6abe06e11f79f2292c7939dc6390027e53e5ba
  React-debug: 660486c6bbf3fd9a3d36082b6c3e2d247bc09b41
  React-Fabric: 5345c138b3a3d7cd7ebd5ff0f7f92523f77ccbc9
  React-FabricImage: 3be67bb7a2a1dd25b1cfec89484410bdeddb675c
  React-graphics: 21b645518ccfbb0850d71b4305db3858ac0944ba
  React-hermes: c2877efac91c02266c66cd62ccef3fa7c36d8604
  React-ImageManager: 8e2d7bc1f7a936772f4d0ad242e504a87a88eb8e
  React-jserrorhandler: 85774b25774fdba4eb88ba501b02893bc6850457
  React-jsi: 5da729c3787b5d58b8612fcd4308290e88af9dde
  React-jsiexecutor: 911f565c4dcb2faf750e274a18012c88355fc33c
  React-jsinspector: a98428936fb888cc15d857226a26d9ac0a668a0e
  React-logger: 6c1170f7bc315878ef4bd3b918e09130cf632798
  React-Mapbuffer: 300b7684f71a676e9f2dbf6d67e14f39adfe84d3
  react-native-background-actions: ea5bab231a28710eea614acb9ca595594415a5ec
  react-native-blob-util: 39a20f2ef11556d958dc4beb0aa07d1ef2690745
  react-native-compressor: 260f813f8b88066aa09bc04020becb1f3f43f3d5
  react-native-config: 8f7283449bbb048902f4e764affbbf24504454af
  react-native-document-picker: 495c444c0c773c6e83a5d91165890ecb1c0a399a
  react-native-google-maps: dd12cdd86b8e3fedbbb671676aedaa2f656e2ccd
  react-native-image-picker: a5dddebb4d2955ac4712a4ed66b00a85f62a63ac
  react-native-image-resizer: 6260ba497fb8d1a593c1c92ccd593f570df6f5b7
  react-native-maps: 667f9b975549c6fa9b1631bf859440f68ebd3b8f
  react-native-netinfo: 48c5f79a84fbc3ba1d28a8b0d04adeda72885fa8
  react-native-pager-view: da490aa1f902c9a5aeecf0909cc975ad0e92e53e
  react-native-pdf: 73accc61238e8e9bc6e376191225028af20b996e
  react-native-photo-editor: df7a23b0f31379ca5f64671f7d9fc42dbdd68722
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: 9e40fb181dac02619414ba1294d6c2a807056ab9
  react-native-slider: 33b8d190b59d4f67a541061bb91775d53d617d9d
  react-native-tracking-transparency: 25ff1ff866e338c137c818bdec20526bb05ffcc1
  react-native-video: c26780b224543c62d5e1b2a7244a5cd1b50e8253
  react-native-webview: 9f111dfbcfc826084d6c507f569e5e03342ee1c1
  React-nativeconfig: 6178939b2ac9010dd2f9f994aab3ab11fa68fbf3
  React-NativeModulesApple: 802f2357d0af3e0eaf57db321987f6a425fd3c6d
  React-perflogger: 3887a05940ccd34a83457fd153fdeda509b31737
  React-RCTActionSheet: 2f42b4797374b53e93b65c79eaa8a0d292e255ac
  React-RCTAnimation: 8d855a38975b065bab2528ccf158734044bcec59
  React-RCTAppDelegate: bddf79fb86d03810269d1af5d6ad06a654479a16
  React-RCTBlob: f4bad11cecd4ed488dcbbda1581ff8fe92746107
  React-RCTFabric: 6d8b3e82114a8c2db17f37c146554938c89f4600
  React-RCTImage: 187d39d7f82dbddaee1c00fe5beb3fb4cd16eee2
  React-RCTLinking: 22568a7c6d2ee51ef4d5c36e9e4d9720f74e9aed
  React-RCTNetwork: 6453b643f665273e31fdfe21cfa5a32666d61685
  React-RCTSettings: 237e7aa727543b5df2e0eecbfc49fc1bf21482b5
  React-RCTText: 9a775859da5b6a1a2e4bebcd358909a77072f7c8
  React-RCTVibration: 0167279571af233088b53ca3c67606b2a355a0f2
  React-rendererdebug: 7ac55d2cacbfde959d81ce9dc5d262926d0081c6
  React-rncore: 8172cb1a801b7473d87d1563fa8ff49cc222ac07
  React-runtimeexecutor: 2fd27b921f664e66d903ee274dcda55cc0b2cb2e
  React-runtimescheduler: 08a4e7fa9735811df202a7dd43703ad7dbd5eb3c
  React-utils: 7ea0aecc0aec186197ba41491992c7d0d4d77660
  ReactCommon: cd1a327b37af7f7c17fda8125faf3f7865320b05
  RNAudioRecorderPlayer: 224c7de87722938aedce04000d09baa633148f5b
  RNCAsyncStorage: cc6479c4acd84cc7004946946c8afe30b018184d
  RNCClipboard: 5e503962f0719ace8f7fdfe9c60282b526305c85
  RNCPicker: 0bc2f0a29abcca7b7ed44a2d036aac9ab6d25700
  RNDateTimePicker: 40ffda97d071a98a10fdca4fa97e3977102ccd14
  RNDeviceInfo: 475a4c447168d0ad4c807e48ef5e0963a0f4eb1b
  RNFBAnalytics: 7f86e9f4eab59a6b2ac1de5000449bceefbd017e
  RNFBApp: b955f412aceea6bb76a5c60191ef4d3e862d8b02
  RNFBCrashlytics: f1a9cb89e0d032bc51c780161e6fc2c5fcf26874
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 2af4f6b5149018506ea348556ef5472d25bbf794
  RNNotifee: f3c01b391dd8e98e67f539f9a35a9cbcd3bae744
  RNPermissions: c7235fc2b9324be1dd511310ecaff2512cb62bff
  RNReanimated: 3d18464923982a13681c6b8b9377c86ad9a12249
  RNScreens: b7eeec2c5d2658dac8c30d2de57dcaabd368d239
  RNSound: 6c156f925295bdc83e8e422e7d8b38d33bc71852
  RNSoundPlayer: 5dabb50ed0cca3808997c91c4f6bbc6d576e2e46
  RNSVG: 3d2bdcaef51c8071880a9c0072fe324f4423a3ba
  RNVectorIcons: fcc2f6cb32f5735b586e66d14103a74ce6ad61f8
  RNZipArchive: 7bb4c70d6aa2dd235212c0a4a3de0a4e237e2569
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: c69881e8ac5521f0e622291387add5f60f30f3c4
  VisionCamera: 36c460338692788a0d377dce7d32f8ba049fb4f2
  Yoga: 91f60b73321722cd6f56167527b0d46dbb482d37

PODFILE CHECKSUM: 67fa22bdb50b69154e3e8133abf889b981aa524f

COCOAPODS: 1.16.2
