{"name": "engine", "version": "1.6.29-86", "private": true, "scripts": {"android": "mkdir -p node_modules && react-native run-android", "ios": "react-native run-ios", "podInstall64": "cd ios && arch -x86_64 pod install", "podInstall": "cd ios && pod install", "lint": "eslint ../.. --ext .js,.jsx,.ts,.tsx", "start": "react-native start", "test": "jest"}, "dependencies": {"@bam.tech/react-native-image-resizer": "3.0.11", "react-native-signature-canvas": "4.5.1", "react-native-pdf": "6.7.7", "react-native-ui-datepicker": "3.0.7", "@baronha/react-native-photo-editor": "1.1.6", "@cycle/http": "^15.4.0", "@cycle/run": "^5.7.0", "@gorhom/bottom-sheet": "^5.0.5", "@miblanchard/react-native-slider": "^2.1.0", "@notifee/react-native": "7.8.0", "@react-native-async-storage/async-storage": "2.1.0", "@react-native-clipboard/clipboard": "1.14.2", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^9.4.1", "@react-native-community/slider": "^4.2.2", "@react-native-firebase/analytics": "^21.5.0", "@react-native-firebase/app": "^21.5.0", "@react-native-firebase/crashlytics": "^21.5.0", "@react-native-picker/picker": "^2.4.10", "@react-navigation/bottom-tabs": "^6.2.0", "@react-navigation/material-top-tabs": "^6.2.1", "@react-navigation/native": "^6.0.7", "@react-navigation/native-stack": "^6.3.0", "@reduxjs/toolkit": "^1.9.3", "@types/react": "^18.0.28", "axios": "^0.25.0", "dayjs": "^1.11.1", "debounce": "^1.2.1", "lodash": "^4.17.21", "moment": "^2.29.1", "react": "18.2.0", "react-content-loader": "7.0.2", "react-hook-form": "^7.52.0", "react-native": "0.73.11", "react-native-audio-recorder-player": "3.6.12", "react-native-background-actions": "^4.0.1", "react-native-blob-util": "^0.19.9", "react-native-bouncy-checkbox": "^4.1.2", "react-native-collapsible": "^1.6.0", "react-native-compressor": "1.8.15", "react-native-config": "^1.5.3", "react-native-device-info": "^10.6.0", "react-native-document-picker": "^8.2.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.20.1", "react-native-image-picker": "^5.0.2", "react-native-indicators": "^0.17.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-map-clustering": "^3.4.2", "react-native-maps": "^1.3.2", "react-native-modal": "^13.0.1", "react-native-mssql": "^0.1.4", "react-native-pager-view": "^5.4.15", "react-native-permissions": "^3.9.2", "react-native-push-notification": "8.1.1", "react-native-reanimated": "^3.16.2", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "^3.3.2", "react-native-screens": "^3.32.0", "react-native-sound": "^0.11.2", "react-native-sound-player": "^0.13.2", "react-native-svg": "^15.9.0", "react-native-svg-transformer": "^1.2.0", "react-native-swipeable-list": "^0.1.2", "react-native-tab-view": "^3.1.1", "react-native-tags": "^2.2.1", "react-native-toast-message": "2.2.1", "react-native-tracking-transparency": "^0.1.2", "react-native-user-avatar": "^1.0.8", "react-native-uuid": "2.0.2", "react-native-vector-icons": "^9.2.0", "react-native-video": "^5.2.1", "react-native-vision-camera": "3.9.2", "react-native-webview": "^11.18.1", "react-native-xml2js": "^1.0.3", "react-native-zip-archive": "7.0.1", "react-query": "^3.34.14", "react-redux": "^7.2.9", "react-timer-mixin": "^0.13.4", "reactotron-redux": "^3.1.3", "redux": "^4.2.1", "redux-cycles": "^0.4.1", "redux-persist": "^6.0.0", "redux-persist-filesystem-storage": "4.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/lodash": "^4.14.178", "@types/react": "^18.2.6", "@types/react-native-indicators": "^0.16.2", "@types/react-native-vector-icons": "^6.4.13", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.7.0", "@typescript-eslint/parser": "^5.7.0", "babel-jest": "^29.6.3", "detox": "^19.7.1", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-dotenv": "^3.4.8", "react-test-renderer": "18.2.0", "reactotron-react-native": "^5.0.1", "typescript": "5.0.4"}, "resolutions": {"@types/react": "^17", "react-devtools-core": "4.13.0"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}