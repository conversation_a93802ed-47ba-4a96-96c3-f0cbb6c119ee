import React, { useEffect } from "react";
import { StyleSheet, View, Platform } from "react-native";
import {
  getTrackingStatus,
  requestTrackingPermission,
} from "react-native-tracking-transparency";

// Components
import TabsNavigation from "../navigation/TabsNavigaton";

// Styles
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import { Theme } from "btheme/ThemeInterface";

type Props = {};

/**
 * Checks and requests App Tracking Transparency permission on iOS
 */
const checkATTPermission = async () => {
  // Only run on iOS devices
  if (Platform.OS === "ios") {
    const trackingStatus = await getTrackingStatus();
    console.log("App Tracking Transparency status:", trackingStatus);

    if (trackingStatus === "not-determined") {
      const permission = await requestTrackingPermission();
      if (permission === "authorized") {
        console.log("User granted tracking permission ✅");
      } else {
        console.log("User denied tracking permission ❌");
      }
    }
  }
};

const ATTPermissionHOC = ({}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  useEffect(() => {
    // Check ATT permission with a slight delay to not interfere with initial loading
    setTimeout(() => {
      checkATTPermission();
    }, 1000);
  }, []);

  return <TabsNavigation />;
};

export default ATTPermissionHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      justifyContent: "center",
      alignItems: "center",
    },
  });

  return { styles, color };
};
