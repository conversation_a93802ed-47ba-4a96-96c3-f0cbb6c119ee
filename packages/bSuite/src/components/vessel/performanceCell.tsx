import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { CustomText } from "bcomponents";

// Styles
import { SPACING, FONTSIZES } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {
  firstValue: number | null;
  secondValue?: number | null;
  showRedTextIf?: boolean;
};

const PerformanceCell = ({ firstValue, secondValue, showRedTextIf }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  // Helper function to format numbers to two decimal places
  const formatNumber = (value: number | null) => {
    return value !== null ? value?.toFixed(2) : "";
  };

  return (
    <View style={styles.cellsContainer}>
      <CustomText
        style={[
          styles.cell,
          {
            color: showRedTextIf
              ? color.DESTRUCTIVE_DEFAULT
              : color.HALF_DIMMED,
          },
        ]}
      >
        {formatNumber(firstValue)}
      </CustomText>

      {secondValue !== undefined ? (
        <Text style={{ color: color.HALF_DIMMED }}> / </Text>
      ) : (
        <></>
      )}

      {secondValue !== undefined ? (
        <CustomText style={styles.cell}>{formatNumber(secondValue)}</CustomText>
      ) : (
        <></>
      )}
    </View>
  );
};

export default PerformanceCell;

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    cellsContainer: {
      paddingVertical: SPACING.SIX,
      margin: SPACING.SIX,
      flex: 1 / 3,
      borderRadius: SPACING.XXS,
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
    },
    cell: {
      fontWeight: "500",
      textAlign: "center",
    },
  });

  return { styles, color, images };
};
