import React, { useEffect } from "react";
import { StyleSheet, View } from "react-native";
import { useSelector } from "react-redux";
import { useIsFocused } from "@react-navigation/native";
import notifee, { AuthorizationStatus } from "@notifee/react-native";
import {
  checkMultiple,
  request,
  PERMISSIONS,
  RESULTS,
} from "react-native-permissions";
import RouteNames from "../navigation/RouteNames";
import {
  getTrackingStatus,
  requestTrackingPermission,
} from "react-native-tracking-transparency";

// Components
import InspectionsCard from "../components/home/<USER>";
import HomeCardLayout from "../components/home/<USER>";
import ChecklistsCard from "../components/home/<USER>";
import ProgressCardsSection from "../components/home/<USER>";

//Styling
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

const checkPermissions = async () => {
  const settings = await notifee.getNotificationSettings();

  if (settings.authorizationStatus == AuthorizationStatus.AUTHORIZED) {
    console.log("Notification permissions have been authorized");
  } else if (settings.authorizationStatus == AuthorizationStatus.DENIED) {
    console.log("Notification permissions have been denied");
    await notifee.requestPermission();
  }

  // Check ATT Status
  const trackingStatus = await getTrackingStatus();
  console.log("Tracking status:", trackingStatus);

  if (trackingStatus === "not-determined") {
    const permission = await requestTrackingPermission();
    if (permission === "authorized") {
      console.log("User granted tracking permission ✅");
    } else {
      console.log("User denied tracking permission ❌");
    }
  }

  const permissions = [
    PERMISSIONS.IOS.MEDIA_LIBRARY,
    PERMISSIONS.IOS.CAMERA,
    PERMISSIONS.IOS.MICROPHONE,
    PERMISSIONS.IOS.PHOTO_LIBRARY,
    PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY,
  ];

  checkMultiple(permissions)
    .then((statuses) => {
      console.log("Permissions statuses: ", statuses);
      for (let i = 0; i < permissions.length; i++) {
        const permission = permissions[i];
        const status = statuses[permission];

        switch (status) {
          case RESULTS.UNAVAILABLE:
            console.log("Feature not available on this device");
            break;
          case RESULTS.DENIED:
            console.log("Permission denied, requesting...");
            request(permission).then((result) => {
              console.log(`Requested permission for ${permission}`, result);
            });
            break;
          case RESULTS.GRANTED:
            console.log(`${permission} is granted ✅`);
            break;
          case RESULTS.BLOCKED:
            console.log(`${permission} is blocked and cannot be requested ❌`);
            break;
        }
      }
    })
    .catch((error) => console.log("Permission error:", error));
};

const Home = ({ navigation }) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const isFocused = useIsFocused();
  const { inspections, attachments, progress, checklists } = useSelector(
    (state) => state.persist.questionReducer
  );
  const inProgress = inspections.allIds.filter(
    (id) => inspections[id].status === "In Progress"
  );
  const uploaded = inspections.allIds.filter(
    (id) => inspections[id].status === "Uploaded"
  );
  const uploading = inspections.allIds.filter(
    (id) => inspections[id].status === "Uploading"
  );
  const failed = inspections.allIds.filter(
    (id) => inspections[id].status === "Failed"
  );
  const newChecklist = checklists.allIds.filter(
    (id) => checklists[id].status === "New"
  );
  const usedChecklist = checklists.allIds.filter(
    (id) => checklists[id].status === "Used"
  );

  useEffect(() => {
    setTimeout(() => {
      checkPermissions();
    }, 3000);
  }, []);

  const handleNavigate = (title) => {
    navigation.navigate(RouteNames.InspectionsListStack, {
      screen: RouteNames.InspectionsList,
      params: { title },
    });
  };

  const handleNavigateToChecklists = () => {
    navigation.navigate(RouteNames.ChecklistsStack);
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.container}>
        <HomeCardLayout
          cardTitle="Inspections"
          bigCard={
            <InspectionsCard
              count={inProgress?.length}
              title="Active"
              handleNavigate={() => handleNavigate("In Progress")}
            />
          }
        >
          <ChecklistsCard
            count={uploading?.length}
            title="Uploading"
          handleNavigate={() => handleNavigate("In Progress")}
          />
          <ChecklistsCard
            count={uploaded?.length}
            title="Uploaded"
            handleNavigate={() => handleNavigate("Uploaded")}
          />
          <ChecklistsCard
            count={failed?.length}
            title="Failed"
            handleNavigate={() => handleNavigate("In Progress")}
          />
        </HomeCardLayout>

        <HomeCardLayout cardTitle="Checklists">
          <ChecklistsCard
            count={newChecklist?.length}
            title="New"
            handleNavigate={handleNavigateToChecklists}
          />
          <ChecklistsCard
            count={usedChecklist?.length}
            title="Used"
            handleNavigate={handleNavigateToChecklists}
          />
        </HomeCardLayout>
      </View>

      <View style={{ flex: 1 }}>{progress && <ProgressCardsSection />}</View>
    </View>
  );
};

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
      padding: SPACING.XS,
    },
    navigationCardsSection: {
      marginBottom: SPACING.M,
    },
    navigationCard: {
      margin: SPACING.XXS,
    },
  });

  return { styles, color, images };
};

export default Home;
