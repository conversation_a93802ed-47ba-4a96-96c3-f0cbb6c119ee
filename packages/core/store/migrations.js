export const migrations = {
  0: (state) => {
    // Migration from version -1 (no version) to version 0
    return {
      ...state,
    };
  },
  1: (state) => {
    return {
      ...state,
      persist: {
        ...state.persist,
        bTeamContactsSlice: state.persist.bTeamContactsSlice || {
          countries: { byId: {}, allIds: [] },
          countriesIsLoading: false,
          countriesErrorText: null,
          cities: { byId: {}, allIds: [] },
          citiesIsLoading: false,
          citiesErrorText: null,
          classifications: { byId: {}, allIds: [] },
          classificationsIsLoading: false,
          classificationsErrorText: null,
          contactTypes: { byId: {}, allIds: [] },
          contactTypesIsLoading: false,
          contactTypesErrorText: null,
          contactingTypes: { byId: {}, allIds: [] },
          contactingTypesIsLoading: false,
          contactingTypesErrorText: null,
          vesselTypes: { byId: {}, allIds: [] },
          vesselTypesIsLoading: false,
          vesselTypesErrorText: null,
          contacts: {
            byId: {},
            allIds: [],
            companyIds: [],
            personIds: [],
            vesselIds: [],
            departmentIds: [],
            branchCompanyIds: [],
          },
          contactsIsLoading: false,
          contactsErrorText: null,
          addresses: { byId: {}, allIds: [] },
          addressesIsLoading: false,
          addressesErrorText: null,
          contactClassifications: { byId: {}, allIds: [] },
          contactClassificationsIsLoading: false,
          contactClassificationsErrorText: null,
          contacting: { byId: {}, allIds: [] },
          contactingIsLoading: false,
          contactingErrorText: null,
          contactsSyncCompletedCount: 0,
        },
        bTeamSyncSlice: state.persist.bTeamSyncSlice || {
          syncEntitiesGroups: { byId: {}, allIds: [] },
          syncEntitiesGroupsIsLoading: false,
          syncEntitiesGroupsErrorText: null,
          syncEntities: { byId: {}, allIds: [] },
          syncEntitiesIsLoading: false,
          syncEntitiesErrorText: null,
          syncContactsIsLoading: false,
          syncContactsErrorText: null,
          syncIsLoading: false,
          syncErrorText: null,
          syncCompletionIsLoading: false,
          syncCompletionErrorText: null,
          syncContactsDate: null,
          hasEverSyncContacts: false,
        },
        bTeamSearchSuggestionsSlice: {
          ...state.persist.bTeamSearchSuggestionsSlice,
          contacts: state.persist.bTeamSearchSuggestionsSlice?.contacts || {
            searchSuggestions: [],
          },
        },
      },
    };
  },
  2: (state) => {
    // Remove searchSuggestions from notificationsSlice
    const { searchSuggestions, ...restNotificationsSlice } = state.persist?.notificationsSlice || {};
    
    return {
      ...state,
      persist: {
        ...state.persist,
        notificationsSlice: restNotificationsSlice
      }
    };
  },
};
